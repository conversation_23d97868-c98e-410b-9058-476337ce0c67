<?php
/**
 * Post Types class
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class for managing custom post types
 */
class VMI_Post_Types {

    /**
     * Constructor
     */
    public function __construct() {
        // Register post types
        add_action( 'init', array( $this, 'register_post_types' ), 0 ); // Priority 0 to ensure they are available early

        // Register taxonomies
        add_action( 'init', array( $this, 'register_taxonomies' ), 0 ); // Priority 0
    }

    /**
     * Register custom post types
     */
    public function register_post_types() {
        // Register Virtual Tours post type
        register_post_type( 'vmi_virtual_tours', array(
            'labels' => array(
                'name'               => _x( 'Virtual Tours', 'post type general name', 'voxel-media-integrator' ),
                'singular_name'      => _x( 'Virtual Tour', 'post type singular name', 'voxel-media-integrator' ),
                'menu_name'          => _x( 'Virtual Tours', 'admin menu', 'voxel-media-integrator' ),
                'name_admin_bar'     => _x( 'Virtual Tour', 'add new on admin bar', 'voxel-media-integrator' ),
                'add_new'            => _x( 'Add New', 'virtual tour', 'voxel-media-integrator' ),
                'add_new_item'       => __( 'Add New Virtual Tour', 'voxel-media-integrator' ),
                'new_item'           => __( 'New Virtual Tour', 'voxel-media-integrator' ),
                'edit_item'          => __( 'Edit Virtual Tour', 'voxel-media-integrator' ),
                'view_item'          => __( 'View Virtual Tour', 'voxel-media-integrator' ),
                'all_items'          => __( 'All Virtual Tours', 'voxel-media-integrator' ),
                'search_items'       => __( 'Search Virtual Tours', 'voxel-media-integrator' ),
                'parent_item_colon'  => __( 'Parent Virtual Tours:', 'voxel-media-integrator' ),
                'not_found'          => __( 'No virtual tours found.', 'voxel-media-integrator' ),
                'not_found_in_trash' => __( 'No virtual tours found in Trash.', 'voxel-media-integrator' ),
            ),
            'public'              => true,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_menu'        => 'vmi-virtual-tours-dashboard', // Matches admin page slug
            'show_in_admin_bar'   => true,
            'query_var'           => true,
            'rewrite'             => array( 'slug' => 'virtual-tour' ),
            'capability_type'     => 'post',
            'has_archive'         => true,
            'hierarchical'        => false,
            'supports'            => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'custom-fields', 'comments' ),
            'show_in_rest'        => true,
            'menu_icon'           => 'dashicons-camera-alt',
        ) );

        // Register 3D Models post type
        register_post_type( 'vmi_3d_models', array(
            'labels' => array(
                'name'               => _x( '3D Models', 'post type general name', 'voxel-media-integrator' ),
                'singular_name'      => _x( '3D Model', 'post type singular name', 'voxel-media-integrator' ),
                'menu_name'          => _x( '3D Models', 'admin menu', 'voxel-media-integrator' ),
                'name_admin_bar'     => _x( '3D Model', 'add new on admin bar', 'voxel-media-integrator' ),
                'add_new'            => _x( 'Add New', '3d model', 'voxel-media-integrator' ),
                'add_new_item'       => __( 'Add New 3D Model', 'voxel-media-integrator' ),
                'new_item'           => __( 'New 3D Model', 'voxel-media-integrator' ),
                'edit_item'          => __( 'Edit 3D Model', 'voxel-media-integrator' ),
                'view_item'          => __( 'View 3D Model', 'voxel-media-integrator' ),
                'all_items'          => __( 'All 3D Models', 'voxel-media-integrator' ),
                'search_items'       => __( 'Search 3D Models', 'voxel-media-integrator' ),
                'parent_item_colon'  => __( 'Parent 3D Models:', 'voxel-media-integrator' ),
                'not_found'          => __( 'No 3D models found.', 'voxel-media-integrator' ),
                'not_found_in_trash' => __( 'No 3D models found in Trash.', 'voxel-media-integrator' ),
            ),
            'public'              => true,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_menu'        => 'vmi-3d-models', // Matches admin page slug
            'show_in_admin_bar'   => true,
            'query_var'           => true,
            'rewrite'             => array( 'slug' => '3d-model' ),
            'capability_type'     => 'post',
            'has_archive'         => true,
            'hierarchical'        => false,
            'supports'            => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'        => true,
            'menu_icon'           => 'dashicons-format-image', 
        ) );

        // Register Videos post type
        register_post_type( 'vmi_videos', array(
            'labels' => array(
                'name'               => _x( 'Videos', 'post type general name', 'voxel-media-integrator' ),
                'singular_name'      => _x( 'Video', 'post type singular name', 'voxel-media-integrator' ),
                'menu_name'          => _x( 'Videos', 'admin menu', 'voxel-media-integrator' ),
                'name_admin_bar'     => _x( 'Video', 'add new on admin bar', 'voxel-media-integrator' ),
                'add_new'            => _x( 'Add New', 'video', 'voxel-media-integrator' ),
                'add_new_item'       => __( 'Add New Video', 'voxel-media-integrator' ),
                'new_item'           => __( 'New Video', 'voxel-media-integrator' ),
                'edit_item'          => __( 'Edit Video', 'voxel-media-integrator' ),
                'view_item'          => __( 'View Video', 'voxel-media-integrator' ),
                'all_items'          => __( 'All Videos', 'voxel-media-integrator' ),
                'search_items'       => __( 'Search Videos', 'voxel-media-integrator' ),
                'parent_item_colon'  => __( 'Parent Videos:', 'voxel-media-integrator' ),
                'not_found'          => __( 'No videos found.', 'voxel-media-integrator' ),
                'not_found_in_trash' => __( 'No videos found in Trash.', 'voxel-media-integrator' ),
            ),
            'public'              => true,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_menu'        => 'vmi-video-dashboard', // Matches admin page slug
            'show_in_admin_bar'   => true,
            'query_var'           => true,
            'rewrite'             => array( 'slug' => 'video' ),
            'capability_type'     => 'post',
            'has_archive'         => true,
            'hierarchical'        => false,
            'supports'            => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'        => true,
            'menu_icon'           => 'dashicons-video-alt3',
        ) );

        // Register Courses post type
        register_post_type( 'vmi_courses', array(
            'labels' => array(
                'name'               => _x( 'Courses', 'post type general name', 'voxel-media-integrator' ),
                'singular_name'      => _x( 'Course', 'post type singular name', 'voxel-media-integrator' ),
                'menu_name'          => _x( 'Courses', 'admin menu', 'voxel-media-integrator' ),
                'name_admin_bar'     => _x( 'Course', 'add new on admin bar', 'voxel-media-integrator' ),
                'add_new'            => _x( 'Add New', 'course', 'voxel-media-integrator' ),
                'add_new_item'       => __( 'Add New Course', 'voxel-media-integrator' ),
                'new_item'           => __( 'New Course', 'voxel-media-integrator' ),
                'edit_item'          => __( 'Edit Course', 'voxel-media-integrator' ),
                'view_item'          => __( 'View Course', 'voxel-media-integrator' ),
                'all_items'          => __( 'All Courses', 'voxel-media-integrator' ),
                'search_items'       => __( 'Search Courses', 'voxel-media-integrator' ),
                'parent_item_colon'  => __( 'Parent Courses:', 'voxel-media-integrator' ),
                'not_found'          => __( 'No courses found.', 'voxel-media-integrator' ),
                'not_found_in_trash' => __( 'No courses found in Trash.', 'voxel-media-integrator' ),
            ),
            'public'              => true,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_menu'        => 'vmi-lms', // Matches admin page slug
            'show_in_admin_bar'   => true,
            'query_var'           => true,
            'rewrite'             => array( 'slug' => 'course' ),
            'capability_type'     => 'post',
            'has_archive'         => true,
            'hierarchical'        => false,
            'supports'            => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'        => true,
            'menu_icon'           => 'dashicons-welcome-learn-more',
        ) );

        // Register Lessons post type
        register_post_type( 'vmi_lessons', array(
            'labels' => array(
                'name'               => _x( 'Lessons', 'post type general name', 'voxel-media-integrator' ),
                'singular_name'      => _x( 'Lesson', 'post type singular name', 'voxel-media-integrator' ),
                'menu_name'          => _x( 'Lessons', 'admin menu', 'voxel-media-integrator' ),
                'name_admin_bar'     => _x( 'Lesson', 'add new on admin bar', 'voxel-media-integrator' ),
                'add_new'            => _x( 'Add New', 'lesson', 'voxel-media-integrator' ),
                'add_new_item'       => __( 'Add New Lesson', 'voxel-media-integrator' ),
                'new_item'           => __( 'New Lesson', 'voxel-media-integrator' ),
                'edit_item'          => __( 'Edit Lesson', 'voxel-media-integrator' ),
                'view_item'          => __( 'View Lesson', 'voxel-media-integrator' ),
                'all_items'          => __( 'All Lessons', 'voxel-media-integrator' ),
                'search_items'       => __( 'Search Lessons', 'voxel-media-integrator' ),
                'parent_item_colon'  => __( 'Parent Lessons:', 'voxel-media-integrator' ),
                'not_found'          => __( 'No lessons found.', 'voxel-media-integrator' ),
                'not_found_in_trash' => __( 'No lessons found in Trash.', 'voxel-media-integrator' ),
            ),
            'public'              => true,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_menu'        => 'vmi-lms', // Matches admin page slug
            'show_in_admin_bar'   => true,
            'query_var'           => true,
            'rewrite'             => array( 'slug' => 'lesson' ),
            'capability_type'     => 'post',
            'has_archive'         => true,
            'hierarchical'        => false,
            'supports'            => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'        => true,
            'menu_icon'           => 'dashicons-book-alt',
        ) );

        // Register Quizzes post type
        register_post_type( 'vmi_quizzes', array(
            'labels' => array(
                'name'               => _x( 'Quizzes', 'post type general name', 'voxel-media-integrator' ),
                'singular_name'      => _x( 'Quiz', 'post type singular name', 'voxel-media-integrator' ),
                'menu_name'          => _x( 'Quizzes', 'admin menu', 'voxel-media-integrator' ),
                'name_admin_bar'     => _x( 'Quiz', 'add new on admin bar', 'voxel-media-integrator' ),
                'add_new'            => _x( 'Add New', 'quiz', 'voxel-media-integrator' ),
                'add_new_item'       => __( 'Add New Quiz', 'voxel-media-integrator' ),
                'new_item'           => __( 'New Quiz', 'voxel-media-integrator' ),
                'edit_item'          => __( 'Edit Quiz', 'voxel-media-integrator' ),
                'view_item'          => __( 'View Quiz', 'voxel-media-integrator' ),
                'all_items'          => __( 'All Quizzes', 'voxel-media-integrator' ),
                'search_items'       => __( 'Search Quizzes', 'voxel-media-integrator' ),
                'parent_item_colon'  => __( 'Parent Quizzes:', 'voxel-media-integrator' ),
                'not_found'          => __( 'No quizzes found.', 'voxel-media-integrator' ),
                'not_found_in_trash' => __( 'No quizzes found in Trash.', 'voxel-media-integrator' ),
            ),
            'public'              => true,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'show_ui'             => true,
            'show_in_menu'        => 'vmi-lms', // Matches admin page slug
            'show_in_admin_bar'   => true,
            'query_var'           => true,
            'rewrite'             => array( 'slug' => 'quiz' ),
            'capability_type'     => 'post',
            'has_archive'         => true,
            'hierarchical'        => false,
            'supports'            => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'custom-fields' ),
            'show_in_rest'        => true,
            'menu_icon'           => 'dashicons-forms',
        ) );
    }

    /**
     * Register custom taxonomies
     */
    public function register_taxonomies() {
        $media_cpts = array( 'vmi_virtual_tours', 'vmi_3d_models', 'vmi_videos' );
        $lms_cpts = array( 'vmi_courses', 'vmi_lessons', 'vmi_quizzes' );

        // Register Media Category taxonomy
        register_taxonomy( 'vmi_media_category', $media_cpts, array(
            'labels' => array(
                'name'              => _x( 'Media Categories', 'taxonomy general name', 'voxel-media-integrator' ),
                'singular_name'     => _x( 'Media Category', 'taxonomy singular name', 'voxel-media-integrator' ),
                'search_items'      => __( 'Search Media Categories', 'voxel-media-integrator' ),
                'all_items'         => __( 'All Media Categories', 'voxel-media-integrator' ),
                'parent_item'       => __( 'Parent Media Category', 'voxel-media-integrator' ),
                'parent_item_colon' => __( 'Parent Media Category:', 'voxel-media-integrator' ),
                'edit_item'         => __( 'Edit Media Category', 'voxel-media-integrator' ),
                'update_item'       => __( 'Update Media Category', 'voxel-media-integrator' ),
                'add_new_item'      => __( 'Add New Media Category', 'voxel-media-integrator' ),
                'new_item_name'     => __( 'New Media Category Name', 'voxel-media-integrator' ),
                'menu_name'         => __( 'Media Categories', 'voxel-media-integrator' ),
            ),
            'hierarchical'      => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'query_var'         => true,
            'rewrite'           => array( 'slug' => 'media-category' ),
            'show_in_rest'      => true,
        ) );

        // Register Media Tag taxonomy
        register_taxonomy( 'vmi_media_tag', $media_cpts, array(
            'labels' => array(
                'name'              => _x( 'Media Tags', 'taxonomy general name', 'voxel-media-integrator' ),
                'singular_name'     => _x( 'Media Tag', 'taxonomy singular name', 'voxel-media-integrator' ),
                'search_items'      => __( 'Search Media Tags', 'voxel-media-integrator' ),
                'all_items'         => __( 'All Media Tags', 'voxel-media-integrator' ),
                'parent_item'       => __( 'Parent Media Tag', 'voxel-media-integrator' ),
                'parent_item_colon' => __( 'Parent Media Tag:', 'voxel-media-integrator' ),
                'edit_item'         => __( 'Edit Media Tag', 'voxel-media-integrator' ),
                'update_item'       => __( 'Update Media Tag', 'voxel-media-integrator' ),
                'add_new_item'      => __( 'Add New Media Tag', 'voxel-media-integrator' ),
                'new_item_name'     => __( 'New Media Tag Name', 'voxel-media-integrator' ),
                'menu_name'         => __( 'Media Tags', 'voxel-media-integrator' ),
            ),
            'hierarchical'      => false,
            'show_ui'           => true,
            'show_admin_column' => true,
            'query_var'         => true,
            'rewrite'           => array( 'slug' => 'media-tag' ),
            'show_in_rest'      => true,
        ) );

        // Register Course Category taxonomy
        register_taxonomy( 'vmi_course_category', $lms_cpts, array(
            'labels' => array(
                'name'              => _x( 'Course Categories', 'taxonomy general name', 'voxel-media-integrator' ),
                'singular_name'     => _x( 'Course Category', 'taxonomy singular name', 'voxel-media-integrator' ),
                'search_items'      => __( 'Search Course Categories', 'voxel-media-integrator' ),
                'all_items'         => __( 'All Course Categories', 'voxel-media-integrator' ),
                'parent_item'       => __( 'Parent Course Category', 'voxel-media-integrator' ),
                'parent_item_colon' => __( 'Parent Course Category:', 'voxel-media-integrator' ),
                'edit_item'         => __( 'Edit Course Category', 'voxel-media-integrator' ),
                'update_item'       => __( 'Update Course Category', 'voxel-media-integrator' ),
                'add_new_item'      => __( 'Add New Course Category', 'voxel-media-integrator' ),
                'new_item_name'     => __( 'New Course Category Name', 'voxel-media-integrator' ),
                'menu_name'         => __( 'Course Categories', 'voxel-media-integrator' ),
            ),
            'hierarchical'      => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'query_var'         => true,
            'rewrite'           => array( 'slug' => 'course-category' ),
            'show_in_rest'      => true,
        ) );

        // Register Course Tag taxonomy
        register_taxonomy( 'vmi_course_tag', $lms_cpts, array(
            'labels' => array(
                'name'              => _x( 'Course Tags', 'taxonomy general name', 'voxel-media-integrator' ),
                'singular_name'     => _x( 'Course Tag', 'taxonomy singular name', 'voxel-media-integrator' ),
                'search_items'      => __( 'Search Course Tags', 'voxel-media-integrator' ),
                'all_items'         => __( 'All Course Tags', 'voxel-media-integrator' ),
                'parent_item'       => __( 'Parent Course Tag', 'voxel-media-integrator' ),
                'parent_item_colon' => __( 'Parent Course Tag:', 'voxel-media-integrator' ),
                'edit_item'         => __( 'Edit Course Tag', 'voxel-media-integrator' ),
                'update_item'       => __( 'Update Course Tag', 'voxel-media-integrator' ),
                'add_new_item'      => __( 'Add New Course Tag', 'voxel-media-integrator' ),
                'new_item_name'     => __( 'New Course Tag Name', 'voxel-media-integrator' ),
                'menu_name'         => __( 'Course Tags', 'voxel-media-integrator' ),
            ),
            'hierarchical'      => false,
            'show_ui'           => true,
            'show_admin_column' => true,
            'query_var'         => true,
            'rewrite'           => array( 'slug' => 'course-tag' ),
            'show_in_rest'      => true,
        ) );
    }

    /**
     * Add meta boxes for custom post types
     */
    public function add_meta_boxes() {
        // 3D Models meta boxes
        add_meta_box(
            'vmi_3d_model_details',
            __( '3D Model Details', 'voxel-media-integrator' ),
            array( $this, 'render_3d_model_meta_box' ),
            'vmi_3d_models',
            'normal',
            'high'
        );

        // Video meta boxes
        add_meta_box(
            'vmi_video_details',
            __( 'Video Details', 'voxel-media-integrator' ),
            array( $this, 'render_video_meta_box' ),
            'vmi_videos',
            'normal',
            'high'
        );

        // Virtual Tour meta boxes
        add_meta_box(
            'vmi_tour_details',
            __( 'Virtual Tour Details', 'voxel-media-integrator' ),
            array( $this, 'render_tour_meta_box' ),
            'vmi_virtual_tours',
            'normal',
            'high'
        );

        // Course meta boxes
        add_meta_box(
            'vmi_course_details',
            __( 'Course Details', 'voxel-media-integrator' ),
            array( $this, 'render_course_meta_box' ),
            'vmi_courses',
            'normal',
            'high'
        );
    }

    /**
     * Render 3D Model meta box
     */
    public function render_3d_model_meta_box( $post ) {
        wp_nonce_field( 'vmi_3d_model_meta_box', 'vmi_3d_model_meta_box_nonce' );

        $model_file = get_post_meta( $post->ID, 'vmi_model_file', true );
        $model_format = get_post_meta( $post->ID, 'vmi_model_format', true );
        $model_size = get_post_meta( $post->ID, 'vmi_model_size', true );

        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th><label for="vmi_model_file">' . __( 'Model File URL', 'voxel-media-integrator' ) . '</label></th>';
        echo '<td><input type="url" id="vmi_model_file" name="vmi_model_file" value="' . esc_attr( $model_file ) . '" class="regular-text" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="vmi_model_format">' . __( 'Model Format', 'voxel-media-integrator' ) . '</label></th>';
        echo '<td>';
        echo '<select id="vmi_model_format" name="vmi_model_format">';
        $formats = array( 'glb' => 'GLB', 'gltf' => 'GLTF', 'obj' => 'OBJ', 'fbx' => 'FBX', 'dae' => 'DAE', 'stl' => 'STL' );
        foreach ( $formats as $value => $label ) {
            echo '<option value="' . esc_attr( $value ) . '"' . selected( $model_format, $value, false ) . '>' . esc_html( $label ) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="vmi_model_size">' . __( 'File Size (bytes)', 'voxel-media-integrator' ) . '</label></th>';
        echo '<td><input type="number" id="vmi_model_size" name="vmi_model_size" value="' . esc_attr( $model_size ) . '" class="regular-text" /></td>';
        echo '</tr>';
        echo '</table>';
    }

    /**
     * Save meta box data
     */
    public function save_meta_boxes( $post_id ) {
        // Check if user has permission to edit the post
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }

        // Save 3D model meta
        if ( isset( $_POST['vmi_3d_model_meta_box_nonce'] ) && wp_verify_nonce( $_POST['vmi_3d_model_meta_box_nonce'], 'vmi_3d_model_meta_box' ) ) {
            if ( isset( $_POST['vmi_model_file'] ) ) {
                update_post_meta( $post_id, 'vmi_model_file', sanitize_url( $_POST['vmi_model_file'] ) );
            }
            if ( isset( $_POST['vmi_model_format'] ) ) {
                update_post_meta( $post_id, 'vmi_model_format', sanitize_text_field( $_POST['vmi_model_format'] ) );
            }
            if ( isset( $_POST['vmi_model_size'] ) ) {
                update_post_meta( $post_id, 'vmi_model_size', intval( $_POST['vmi_model_size'] ) );
            }
        }

        // Save video meta
        if ( isset( $_POST['vmi_video_meta_box_nonce'] ) && wp_verify_nonce( $_POST['vmi_video_meta_box_nonce'], 'vmi_video_meta_box' ) ) {
            if ( isset( $_POST['vmi_video_url'] ) ) {
                update_post_meta( $post_id, 'vmi_video_url', sanitize_url( $_POST['vmi_video_url'] ) );
            }
            if ( isset( $_POST['vmi_video_type'] ) ) {
                update_post_meta( $post_id, 'vmi_video_type', sanitize_text_field( $_POST['vmi_video_type'] ) );
            }
            if ( isset( $_POST['vmi_video_duration'] ) ) {
                update_post_meta( $post_id, 'vmi_video_duration', intval( $_POST['vmi_video_duration'] ) );
            }
        }
    }

    /**
     * Render video meta box
     */
    public function render_video_meta_box( $post ) {
        wp_nonce_field( 'vmi_video_meta_box', 'vmi_video_meta_box_nonce' );

        $video_url = get_post_meta( $post->ID, 'vmi_video_url', true );
        $video_type = get_post_meta( $post->ID, 'vmi_video_type', true );
        $video_duration = get_post_meta( $post->ID, 'vmi_video_duration', true );

        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th><label for="vmi_video_url">' . __( 'Video URL', 'voxel-media-integrator' ) . '</label></th>';
        echo '<td><input type="url" id="vmi_video_url" name="vmi_video_url" value="' . esc_attr( $video_url ) . '" class="regular-text" placeholder="https://..." /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="vmi_video_type">' . __( 'Video Type', 'voxel-media-integrator' ) . '</label></th>';
        echo '<td>';
        echo '<select id="vmi_video_type" name="vmi_video_type">';
        $types = array(
            'self_hosted' => __( 'Self Hosted', 'voxel-media-integrator' ),
            'youtube' => __( 'YouTube', 'voxel-media-integrator' ),
            'vimeo' => __( 'Vimeo', 'voxel-media-integrator' )
        );
        foreach ( $types as $value => $label ) {
            echo '<option value="' . esc_attr( $value ) . '"' . selected( $video_type, $value, false ) . '>' . esc_html( $label ) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="vmi_video_duration">' . __( 'Duration (seconds)', 'voxel-media-integrator' ) . '</label></th>';
        echo '<td><input type="number" id="vmi_video_duration" name="vmi_video_duration" value="' . esc_attr( $video_duration ) . '" class="regular-text" min="0" /></td>';
        echo '</tr>';
        echo '</table>';

        echo '<p class="description">' . __( 'For YouTube and Vimeo videos, paste the full URL. For self-hosted videos, upload the video to your media library and paste the URL here.', 'voxel-media-integrator' ) . '</p>';
    }

    /**
     * Render tour meta box (placeholder)
     */
    public function render_tour_meta_box( $post ) {
        echo '<p>' . __( 'Virtual tour meta box content will be added here.', 'voxel-media-integrator' ) . '</p>';
    }

    /**
     * Render course meta box (placeholder)
     */
    public function render_course_meta_box( $post ) {
        echo '<p>' . __( 'Course meta box content will be added here.', 'voxel-media-integrator' ) . '</p>';
    }
} // End of VMI_Post_Types class
