<?php
/**
 * Template for displaying 3D models
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-models">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($draft_models > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft 3D model that needs attention.',
                    'You have %d draft 3D models that need attention.',
                    $draft_models,
                    'voxel-media-integrator'
                ),
                $draft_models
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-media-interactive"></span>
                <?php esc_html_e('Published Models', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_models); ?></p>
            <p class="vmi-stat-label"><?php
                echo esc_html(sprintf(
                    __('%d in review', 'voxel-media-integrator'),
                    $pending_models
                ));
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-admin-appearance"></span>
                <?php esc_html_e('Model Types', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($total_model_types); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Different formats supported', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-chart-area"></span>
                <?php esc_html_e('Storage Used', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php
                echo wp_kses_post(VMI_Admin::format_storage_size($storage_used));
            ?></p>
            <p class="vmi-stat-label"><?php
                echo esc_html(sprintf(
                    __('%s available', 'voxel-media-integrator'),
                    VMI_Admin::format_storage_size($storage_available)
                ));
            ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('post-new.php?post_type=' . VMI_Models::CPT_SLUG)); ?>" class="button button-primary">
            <span class="dashicons dashicons-plus-alt2"></span>
            <?php esc_html_e('Add New Model', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('edit.php?post_type=' . VMI_Models::CPT_SLUG)); ?>" class="button">
            <span class="dashicons dashicons-list-view"></span>
            <?php esc_html_e('View All Models', 'voxel-media-integrator'); ?>
        </a>

        <a href="#vmi-models-settings" class="button vmi-settings-toggle">
            <span class="dashicons dashicons-admin-generic"></span>
            <?php esc_html_e('3D Models Settings', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <!-- 3D Models Settings Panel -->
    <div id="vmi-models-settings" class="vmi-settings-panel" style="display: none;">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-admin-generic"></span>
                <?php esc_html_e('3D Models Settings', 'voxel-media-integrator'); ?>
            </h2>

            <form method="post" action="options.php" class="vmi-settings-form">
                <?php
                settings_fields('vmi_3d_models_settings');
                $models_settings = get_option('vmi_3d_models_settings', array());
                ?>

                <div class="vmi-settings-grid">
                    <!-- Supported Formats -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('Supported Formats', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label><?php esc_html_e('Allowed 3D Model Formats', 'voxel-media-integrator'); ?></label>
                            <div class="vmi-checkbox-group">
                                <label>
                                    <input type="checkbox" name="vmi_3d_models_settings[format_glb]" value="1"
                                        <?php checked(isset($models_settings['format_glb']) ? $models_settings['format_glb'] : 1, 1); ?>>
                                    <?php esc_html_e('GLB (Recommended)', 'voxel-media-integrator'); ?>
                                </label>
                                <label>
                                    <input type="checkbox" name="vmi_3d_models_settings[format_gltf]" value="1"
                                        <?php checked(isset($models_settings['format_gltf']) ? $models_settings['format_gltf'] : 1, 1); ?>>
                                    <?php esc_html_e('GLTF', 'voxel-media-integrator'); ?>
                                </label>
                                <label>
                                    <input type="checkbox" name="vmi_3d_models_settings[format_obj]" value="1"
                                        <?php checked(isset($models_settings['format_obj']) ? $models_settings['format_obj'] : 1, 1); ?>>
                                    <?php esc_html_e('OBJ', 'voxel-media-integrator'); ?>
                                </label>
                                <label>
                                    <input type="checkbox" name="vmi_3d_models_settings[format_fbx]" value="1"
                                        <?php checked(isset($models_settings['format_fbx']) ? $models_settings['format_fbx'] : 0, 1); ?>>
                                    <?php esc_html_e('FBX', 'voxel-media-integrator'); ?>
                                </label>
                                <label>
                                    <input type="checkbox" name="vmi_3d_models_settings[format_dae]" value="1"
                                        <?php checked(isset($models_settings['format_dae']) ? $models_settings['format_dae'] : 0, 1); ?>>
                                    <?php esc_html_e('DAE (Collada)', 'voxel-media-integrator'); ?>
                                </label>
                                <label>
                                    <input type="checkbox" name="vmi_3d_models_settings[format_stl]" value="1"
                                        <?php checked(isset($models_settings['format_stl']) ? $models_settings['format_stl'] : 0, 1); ?>>
                                    <?php esc_html_e('STL', 'voxel-media-integrator'); ?>
                                </label>
                            </div>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_max_upload_size"><?php esc_html_e('Maximum Upload Size (MB)', 'voxel-media-integrator'); ?></label>
                            <input type="number" id="vmi_max_upload_size" name="vmi_3d_models_settings[max_upload_size]"
                                value="<?php echo esc_attr(isset($models_settings['max_upload_size']) ? $models_settings['max_upload_size'] : 50); ?>"
                                min="1" max="<?php echo (int) wp_max_upload_size() / 1024 / 1024; ?>" step="1">
                            <p class="description">
                                <?php esc_html_e('Maximum upload size for 3D models. Server limit: ', 'voxel-media-integrator'); ?>
                                <?php echo esc_html(size_format(wp_max_upload_size())); ?>
                            </p>
                        </div>
                    </div>

                    <!-- Display Settings -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('Display Settings', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_default_model_viewer"><?php esc_html_e('Default Model Viewer', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_default_model_viewer" name="vmi_3d_models_settings[default_model_viewer]">
                                <option value="model-viewer" <?php selected(isset($models_settings['default_model_viewer']) ? $models_settings['default_model_viewer'] : 'model-viewer', 'model-viewer'); ?>>
                                    <?php esc_html_e('Model Viewer (Recommended)', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="three" <?php selected(isset($models_settings['default_model_viewer']) ? $models_settings['default_model_viewer'] : 'model-viewer', 'three'); ?>>
                                    <?php esc_html_e('Three.js', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Choose the default 3D model viewer technology.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_default_model_height"><?php esc_html_e('Default Model Height (px)', 'voxel-media-integrator'); ?></label>
                            <input type="number" id="vmi_default_model_height" name="vmi_3d_models_settings[default_model_height]"
                                value="<?php echo esc_attr(isset($models_settings['default_model_height']) ? $models_settings['default_model_height'] : 400); ?>"
                                min="200" max="1080" step="10">
                            <p class="description"><?php esc_html_e('Default height for 3D model viewers in pixels.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_auto_rotate">
                                <input type="checkbox" id="vmi_auto_rotate" name="vmi_3d_models_settings[auto_rotate]" value="1"
                                    <?php checked(isset($models_settings['auto_rotate']) ? $models_settings['auto_rotate'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Auto-Rotation', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Models will automatically rotate when displayed.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_timeline">
                                <input type="checkbox" id="vmi_enable_timeline" name="vmi_3d_models_settings[enable_timeline]" value="1"
                                    <?php checked(isset($models_settings['enable_timeline']) ? $models_settings['enable_timeline'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Timeline Integration', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow users to share 3D models in the Voxel timeline.', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>

                    <!-- Integration Settings -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('Integration Settings', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_business_field_id"><?php esc_html_e('Business Field ID', 'voxel-media-integrator'); ?></label>
                            <input type="text" id="vmi_business_field_id" name="vmi_3d_models_settings[business_field_id]"
                                value="<?php echo esc_attr(isset($models_settings['business_field_id']) ? $models_settings['business_field_id'] : ''); ?>">
                            <p class="description"><?php esc_html_e('Enter the field ID used to associate 3D models with business listings.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_model_moderation"><?php esc_html_e('Model Moderation', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_model_moderation" name="vmi_3d_models_settings[model_moderation]">
                                <option value="publish" <?php selected(isset($models_settings['model_moderation']) ? $models_settings['model_moderation'] : 'pending', 'publish'); ?>>
                                    <?php esc_html_e('Auto-publish models', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="pending" <?php selected(isset($models_settings['model_moderation']) ? $models_settings['model_moderation'] : 'pending', 'pending'); ?>>
                                    <?php esc_html_e('Require admin approval', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Choose whether 3D models need approval before publishing.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_max_models"><?php esc_html_e('Maximum Models Per User', 'voxel-media-integrator'); ?></label>
                            <input type="number" id="vmi_max_models" name="vmi_3d_models_settings[max_models]"
                                value="<?php echo esc_attr(isset($models_settings['max_models']) ? $models_settings['max_models'] : 0); ?>"
                                min="0" step="1">
                            <p class="description"><?php esc_html_e('Maximum number of 3D models a user can upload (0 = unlimited).', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="vmi-settings-actions">
                    <?php submit_button(__('Save 3D Models Settings', 'voxel-media-integrator'), 'primary', 'submit', false); ?>
                    <button type="button" class="button vmi-settings-toggle"><?php esc_html_e('Cancel', 'voxel-media-integrator'); ?></button>
                </div>
            </form>
        </div>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Models', 'voxel-media-integrator'); ?>
            </h2>

            <?php if (!empty($recent_models)): ?>
                <?php foreach ($recent_models as $model): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-media-interactive"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url(get_edit_post_link($model->ID)); ?>">
                                    <?php echo esc_html($model->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php
                                    $type = get_post_meta($model->ID, 'vmi_model_type', true);
                                    echo sprintf(
                                        '%s &bull; %s',
                                        esc_html(strtoupper($type)),
                                        esc_html(sprintf(
                                            __('Added %s ago', 'voxel-media-integrator'),
                                            human_time_diff(get_post_time('U', false, $model), current_time('timestamp'))
                                        ))
                                    );
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent models found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Model Types Distribution', 'voxel-media-integrator'); ?>
            </h2>

            <div class="vmi-type-distribution">
                <?php foreach ($model_types_stats as $type => $count): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-media-interactive"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <?php echo esc_html(strtoupper($type)); ?>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php echo esc_html(sprintf(
                                    _n('%d model', '%d models', $count, 'voxel-media-integrator'),
                                    $count
                                )); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<style>
.vmi-checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 8px;
}

.vmi-checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.vmi-checkbox-group label:hover {
    background: #e9ecef;
    border-color: #0073aa;
}

.vmi-checkbox-group input[type="checkbox"]:checked + label,
.vmi-checkbox-group label:has(input[type="checkbox"]:checked) {
    background: #e7f3ff;
    border-color: #0073aa;
    color: #0073aa;
}

.vmi-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vmi-setting-group {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
}

.vmi-setting-group h3 {
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #f1f1f1;
    color: #23282d;
    font-size: 16px;
}

.vmi-setting-item {
    margin-bottom: 20px;
}

.vmi-setting-item:last-child {
    margin-bottom: 0;
}

.vmi-setting-item label {
    font-weight: 600;
    color: #23282d;
    margin-bottom: 5px;
    display: block;
}

.vmi-setting-item input[type="number"],
.vmi-setting-item input[type="text"],
.vmi-setting-item select {
    width: 100%;
    max-width: 300px;
}

.vmi-settings-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 10px;
    align-items: center;
}
</style>

<script>
jQuery(document).ready(function($) {
    // 3D Models settings toggle functionality
    $('.vmi-settings-toggle').on('click', function(e) {
        e.preventDefault();
        const settingsPanel = $('#vmi-models-settings');

        if (settingsPanel.is(':visible')) {
            settingsPanel.slideUp(300);
        } else {
            settingsPanel.slideDown(300);
        }
    });

    // Auto-hide settings panel when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#vmi-models-settings, .vmi-settings-toggle').length) {
            $('#vmi-models-settings').slideUp(300);
        }
    });

    // Form validation and enhanced UX
    $('#vmi-models-settings form').on('submit', function(e) {
        // Add loading state
        const submitBtn = $(this).find('input[type="submit"]');
        const originalText = submitBtn.val();

        submitBtn.val('<?php esc_html_e('Saving...', 'voxel-media-integrator'); ?>').prop('disabled', true);

        // Re-enable after a delay (WordPress will handle the actual save)
        setTimeout(function() {
            submitBtn.val(originalText).prop('disabled', false);
        }, 2000);
    });
});
</script>
