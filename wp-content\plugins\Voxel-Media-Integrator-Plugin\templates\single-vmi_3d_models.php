<?php
/**
 * Template for displaying single 3D models
 *
 * @package VoxelMediaIntegrator
 */

get_header(); ?>

<div class="vmi-single-model-container">
    <?php while ( have_posts() ) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class( 'vmi-single-model' ); ?>>
            
            <header class="vmi-model-header">
                <h1 class="vmi-model-title"><?php the_title(); ?></h1>
                
                <div class="vmi-model-meta">
                    <span class="vmi-model-date">
                        <i class="dashicons dashicons-calendar-alt"></i>
                        <?php echo get_the_date(); ?>
                    </span>
                    
                    <span class="vmi-model-author">
                        <i class="dashicons dashicons-admin-users"></i>
                        <?php the_author(); ?>
                    </span>
                    
                    <?php
                    $model_format = get_post_meta( get_the_ID(), 'vmi_model_format', true );
                    if ( $model_format ) : ?>
                        <span class="vmi-model-format">
                            <i class="dashicons dashicons-media-interactive"></i>
                            <?php echo esc_html( strtoupper( $model_format ) ); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php
                    $model_size = get_post_meta( get_the_ID(), 'vmi_model_size', true );
                    if ( $model_size ) : ?>
                        <span class="vmi-model-size">
                            <i class="dashicons dashicons-chart-area"></i>
                            <?php echo esc_html( size_format( $model_size ) ); ?>
                        </span>
                    <?php endif; ?>
                </div>
            </header>

            <div class="vmi-model-viewer-container">
                <?php
                $model_file = get_post_meta( get_the_ID(), 'vmi_model_file', true );
                if ( $model_file ) :
                    echo do_shortcode( '[vmi_3d_model id="' . get_the_ID() . '" width="100%" height="500px"]' );
                else : ?>
                    <div class="vmi-no-model">
                        <p><?php esc_html_e( 'No 3D model file available for this item.', 'voxel-media-integrator' ); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="vmi-model-content">
                <?php if ( has_excerpt() ) : ?>
                    <div class="vmi-model-excerpt">
                        <?php the_excerpt(); ?>
                    </div>
                <?php endif; ?>

                <div class="vmi-model-description">
                    <?php the_content(); ?>
                </div>
            </div>

            <div class="vmi-model-details">
                <h3><?php esc_html_e( 'Model Details', 'voxel-media-integrator' ); ?></h3>
                
                <div class="vmi-details-grid">
                    <?php if ( $model_format ) : ?>
                        <div class="vmi-detail-item">
                            <strong><?php esc_html_e( 'Format:', 'voxel-media-integrator' ); ?></strong>
                            <span><?php echo esc_html( strtoupper( $model_format ) ); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ( $model_size ) : ?>
                        <div class="vmi-detail-item">
                            <strong><?php esc_html_e( 'File Size:', 'voxel-media-integrator' ); ?></strong>
                            <span><?php echo esc_html( size_format( $model_size ) ); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="vmi-detail-item">
                        <strong><?php esc_html_e( 'Published:', 'voxel-media-integrator' ); ?></strong>
                        <span><?php echo get_the_date(); ?></span>
                    </div>
                    
                    <div class="vmi-detail-item">
                        <strong><?php esc_html_e( 'Author:', 'voxel-media-integrator' ); ?></strong>
                        <span><?php the_author(); ?></span>
                    </div>
                </div>
            </div>

            <?php
            // Display categories and tags
            $categories = get_the_terms( get_the_ID(), 'vmi_media_category' );
            $tags = get_the_terms( get_the_ID(), 'vmi_media_tag' );
            
            if ( $categories || $tags ) : ?>
                <div class="vmi-model-taxonomy">
                    <?php if ( $categories ) : ?>
                        <div class="vmi-model-categories">
                            <strong><?php esc_html_e( 'Categories:', 'voxel-media-integrator' ); ?></strong>
                            <?php
                            $category_links = array();
                            foreach ( $categories as $category ) {
                                $category_links[] = '<a href="' . esc_url( get_term_link( $category ) ) . '">' . esc_html( $category->name ) . '</a>';
                            }
                            echo implode( ', ', $category_links );
                            ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ( $tags ) : ?>
                        <div class="vmi-model-tags">
                            <strong><?php esc_html_e( 'Tags:', 'voxel-media-integrator' ); ?></strong>
                            <?php
                            $tag_links = array();
                            foreach ( $tags as $tag ) {
                                $tag_links[] = '<a href="' . esc_url( get_term_link( $tag ) ) . '">' . esc_html( $tag->name ) . '</a>';
                            }
                            echo implode( ', ', $tag_links );
                            ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="vmi-model-actions">
                <a href="<?php echo esc_url( get_post_type_archive_link( 'vmi_3d_models' ) ); ?>" class="vmi-back-to-models">
                    <i class="dashicons dashicons-arrow-left-alt"></i>
                    <?php esc_html_e( 'Back to 3D Models', 'voxel-media-integrator' ); ?>
                </a>
                
                <?php if ( $model_file ) : ?>
                    <a href="<?php echo esc_url( $model_file ); ?>" class="vmi-download-model" download>
                        <i class="dashicons dashicons-download"></i>
                        <?php esc_html_e( 'Download Model', 'voxel-media-integrator' ); ?>
                    </a>
                <?php endif; ?>
            </div>

            <?php
            // Display related models
            $related_models = get_posts( array(
                'post_type' => 'vmi_3d_models',
                'posts_per_page' => 3,
                'post__not_in' => array( get_the_ID() ),
                'orderby' => 'rand',
                'post_status' => 'publish',
            ) );
            
            if ( $related_models ) : ?>
                <div class="vmi-related-models">
                    <h3><?php esc_html_e( 'Related 3D Models', 'voxel-media-integrator' ); ?></h3>
                    
                    <div class="vmi-related-grid">
                        <?php foreach ( $related_models as $related_model ) : ?>
                            <div class="vmi-related-item">
                                <a href="<?php echo esc_url( get_permalink( $related_model->ID ) ); ?>">
                                    <?php if ( has_post_thumbnail( $related_model->ID ) ) : ?>
                                        <?php echo get_the_post_thumbnail( $related_model->ID, 'medium' ); ?>
                                    <?php else : ?>
                                        <div class="vmi-no-thumbnail">
                                            <i class="dashicons dashicons-media-interactive"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <h4><?php echo esc_html( $related_model->post_title ); ?></h4>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

        </article>

        <?php
        // Display comments if enabled
        if ( comments_open() || get_comments_number() ) :
            comments_template();
        endif;
        ?>

    <?php endwhile; ?>
</div>

<style>
.vmi-single-model-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.vmi-model-header {
    text-align: center;
    margin-bottom: 30px;
}

.vmi-model-title {
    font-size: 2.5em;
    margin-bottom: 15px;
    color: #2c3e50;
}

.vmi-model-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    color: #7f8c8d;
    font-size: 14px;
}

.vmi-model-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.vmi-model-viewer-container {
    margin: 30px 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.vmi-model-content {
    margin: 30px 0;
    line-height: 1.8;
}

.vmi-model-excerpt {
    font-size: 1.2em;
    color: #5a6c7d;
    font-style: italic;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-left: 4px solid #3498db;
    border-radius: 4px;
}

.vmi-model-details {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 25px;
    margin: 30px 0;
}

.vmi-model-details h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.vmi-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.vmi-detail-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #f1f1f1;
}

.vmi-model-taxonomy {
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.vmi-model-taxonomy > div {
    margin-bottom: 10px;
}

.vmi-model-taxonomy a {
    color: #3498db;
    text-decoration: none;
    margin-right: 5px;
}

.vmi-model-taxonomy a:hover {
    text-decoration: underline;
}

.vmi-model-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin: 30px 0;
    flex-wrap: wrap;
}

.vmi-model-actions a {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.vmi-model-actions a:hover {
    background: #2980b9;
}

.vmi-related-models {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #e1e5e9;
}

.vmi-related-models h3 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 25px;
}

.vmi-related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.vmi-related-item {
    text-align: center;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vmi-related-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.vmi-related-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.vmi-no-thumbnail {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #7f8c8d;
    font-size: 48px;
}

.vmi-related-item h4 {
    padding: 15px;
    margin: 0;
    color: #2c3e50;
}

.vmi-related-item a {
    text-decoration: none;
    color: inherit;
}

.vmi-no-model {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    color: #7f8c8d;
}

@media (max-width: 768px) {
    .vmi-model-meta {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .vmi-details-grid {
        grid-template-columns: 1fr;
    }
    
    .vmi-model-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .vmi-related-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
