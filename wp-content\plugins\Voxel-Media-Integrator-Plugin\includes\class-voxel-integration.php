<?php
/**
 * Voxel Theme Integration
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class for Voxel theme integration
 */
class VMI_Voxel_Integration {

    /**
     * Constructor
     */
    public function __construct() {
        // Check if Voxel theme is active
        if ( ! $this->is_voxel_active() ) {
            return;
        }

        add_action( 'init', array( $this, 'register_voxel_field_types' ) );
        add_action( 'voxel/app/setup', array( $this, 'setup_voxel_integration' ) );
        add_filter( 'voxel/post-types/config', array( $this, 'add_media_post_types' ) );
        add_filter( 'voxel/timeline/post-types', array( $this, 'add_timeline_support' ) );
        add_action( 'voxel/frontend/before-head', array( $this, 'enqueue_voxel_assets' ) );
    }

    /**
     * Check if Voxel theme is active
     *
     * @return bool True if Voxel is active.
     */
    private function is_voxel_active() {
        return function_exists( '\Voxel\config' ) || class_exists( 'Voxel' );
    }

    /**
     * Register custom field types for Voxel
     */
    public function register_voxel_field_types() {
        if ( ! function_exists( '\Voxel\Field_Types\register' ) ) {
            return;
        }

        // Register 3D Model field type
        \Voxel\Field_Types\register( 'vmi_3d_model', array(
            'label' => __( '3D Model', 'voxel-media-integrator' ),
            'class' => 'VMI_Voxel_3D_Model_Field',
            'icon' => 'las la-cube',
            'category' => 'media',
        ) );

        // Register Video field type
        \Voxel\Field_Types\register( 'vmi_video', array(
            'label' => __( 'Video', 'voxel-media-integrator' ),
            'class' => 'VMI_Voxel_Video_Field',
            'icon' => 'las la-video',
            'category' => 'media',
        ) );

        // Register Virtual Tour field type
        \Voxel\Field_Types\register( 'vmi_virtual_tour', array(
            'label' => __( 'Virtual Tour', 'voxel-media-integrator' ),
            'class' => 'VMI_Voxel_Virtual_Tour_Field',
            'icon' => 'las la-street-view',
            'category' => 'media',
        ) );

        // Register Media Gallery field type
        \Voxel\Field_Types\register( 'vmi_media_gallery', array(
            'label' => __( 'Media Gallery', 'voxel-media-integrator' ),
            'class' => 'VMI_Voxel_Media_Gallery_Field',
            'icon' => 'las la-images',
            'category' => 'media',
        ) );
    }

    /**
     * Setup Voxel integration
     */
    public function setup_voxel_integration() {
        // Add media types to search
        add_filter( 'voxel/search/post-types', array( $this, 'add_search_post_types' ) );
        
        // Add media fields to business listings
        add_filter( 'voxel/post-type/place/fields', array( $this, 'add_business_media_fields' ) );
        
        // Add media to user profiles
        add_filter( 'voxel/user/fields', array( $this, 'add_user_media_fields' ) );
    }

    /**
     * Add media post types to Voxel config
     *
     * @param array $post_types Existing post types.
     * @return array Modified post types.
     */
    public function add_media_post_types( $post_types ) {
        $media_post_types = array(
            'vmi_3d_models' => array(
                'label' => __( '3D Models', 'voxel-media-integrator' ),
                'singular' => __( '3D Model', 'voxel-media-integrator' ),
                'icon' => 'las la-cube',
                'templates' => array(
                    'single' => 'single-vmi_3d_models',
                    'archive' => 'archive-vmi_3d_models',
                    'card' => 'vmi-3d-model-card',
                ),
                'search' => array(
                    'enabled' => true,
                    'filters' => array( 'keywords', 'location', 'category' ),
                ),
                'timeline' => array(
                    'enabled' => true,
                    'wall' => true,
                ),
            ),
            'vmi_videos' => array(
                'label' => __( 'Videos', 'voxel-media-integrator' ),
                'singular' => __( 'Video', 'voxel-media-integrator' ),
                'icon' => 'las la-video',
                'templates' => array(
                    'single' => 'single-vmi_videos',
                    'archive' => 'archive-vmi_videos',
                    'card' => 'vmi-video-card',
                ),
                'search' => array(
                    'enabled' => true,
                    'filters' => array( 'keywords', 'location', 'category' ),
                ),
                'timeline' => array(
                    'enabled' => true,
                    'wall' => true,
                ),
            ),
            'vmi_virtual_tours' => array(
                'label' => __( 'Virtual Tours', 'voxel-media-integrator' ),
                'singular' => __( 'Virtual Tour', 'voxel-media-integrator' ),
                'icon' => 'las la-street-view',
                'templates' => array(
                    'single' => 'single-vmi_virtual_tours',
                    'archive' => 'archive-vmi_virtual_tours',
                    'card' => 'vmi-virtual-tour-card',
                ),
                'search' => array(
                    'enabled' => true,
                    'filters' => array( 'keywords', 'location', 'category' ),
                ),
                'timeline' => array(
                    'enabled' => true,
                    'wall' => true,
                ),
            ),
        );

        return array_merge( $post_types, $media_post_types );
    }

    /**
     * Add media post types to timeline
     *
     * @param array $post_types Timeline post types.
     * @return array Modified post types.
     */
    public function add_timeline_support( $post_types ) {
        $media_types = array( 'vmi_3d_models', 'vmi_videos', 'vmi_virtual_tours' );
        return array_merge( $post_types, $media_types );
    }

    /**
     * Add media post types to search
     *
     * @param array $post_types Search post types.
     * @return array Modified post types.
     */
    public function add_search_post_types( $post_types ) {
        $media_types = array( 'vmi_3d_models', 'vmi_videos', 'vmi_virtual_tours' );
        return array_merge( $post_types, $media_types );
    }

    /**
     * Add media fields to business listings
     *
     * @param array $fields Existing fields.
     * @return array Modified fields.
     */
    public function add_business_media_fields( $fields ) {
        $media_fields = array(
            'vmi_business_gallery' => array(
                'type' => 'vmi_media_gallery',
                'label' => __( 'Media Gallery', 'voxel-media-integrator' ),
                'description' => __( 'Add photos, videos, 3D models, and virtual tours', 'voxel-media-integrator' ),
                'required' => false,
                'props' => array(
                    'allowed_types' => array( '3d_models', 'videos', 'virtual_tours', 'images' ),
                    'max_items' => 20,
                    'enable_reorder' => true,
                ),
            ),
            'vmi_featured_tour' => array(
                'type' => 'vmi_virtual_tour',
                'label' => __( 'Featured Virtual Tour', 'voxel-media-integrator' ),
                'description' => __( 'Showcase your business with a virtual tour', 'voxel-media-integrator' ),
                'required' => false,
                'props' => array(
                    'display_mode' => 'embed',
                    'auto_load' => true,
                ),
            ),
            'vmi_3d_showcase' => array(
                'type' => 'vmi_3d_model',
                'label' => __( '3D Showcase', 'voxel-media-integrator' ),
                'description' => __( 'Display 3D models of your products or space', 'voxel-media-integrator' ),
                'required' => false,
                'props' => array(
                    'display_mode' => 'viewer',
                    'auto_rotate' => true,
                    'camera_controls' => true,
                ),
            ),
        );

        return array_merge( $fields, $media_fields );
    }

    /**
     * Add media fields to user profiles
     *
     * @param array $fields Existing fields.
     * @return array Modified fields.
     */
    public function add_user_media_fields( $fields ) {
        $user_media_fields = array(
            'vmi_user_portfolio' => array(
                'type' => 'vmi_media_gallery',
                'label' => __( 'Portfolio', 'voxel-media-integrator' ),
                'description' => __( 'Showcase your work with media gallery', 'voxel-media-integrator' ),
                'required' => false,
                'props' => array(
                    'allowed_types' => array( '3d_models', 'videos', 'virtual_tours', 'images' ),
                    'max_items' => 10,
                    'enable_reorder' => true,
                ),
            ),
            'vmi_user_demo_reel' => array(
                'type' => 'vmi_video',
                'label' => __( 'Demo Reel', 'voxel-media-integrator' ),
                'description' => __( 'Add a video showcasing your skills', 'voxel-media-integrator' ),
                'required' => false,
                'props' => array(
                    'allowed_sources' => array( 'youtube', 'vimeo', 'self_hosted' ),
                    'auto_play' => false,
                ),
            ),
        );

        return array_merge( $fields, $user_media_fields );
    }

    /**
     * Enqueue Voxel-specific assets
     */
    public function enqueue_voxel_assets() {
        wp_enqueue_style( 'vmi-voxel-integration', VMI_PLUGIN_URL . 'assets/css/voxel-integration.css', array(), VMI_VERSION );
        wp_enqueue_script( 'vmi-voxel-integration', VMI_PLUGIN_URL . 'assets/js/voxel-integration.js', array( 'jquery' ), VMI_VERSION, true );
        
        wp_localize_script( 'vmi-voxel-integration', 'vmi_voxel', array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'vmi_voxel_nonce' ),
            'strings' => array(
                'loading' => __( 'Loading...', 'voxel-media-integrator' ),
                'error' => __( 'An error occurred', 'voxel-media-integrator' ),
                'no_media' => __( 'No media found', 'voxel-media-integrator' ),
            ),
        ) );
    }

    /**
     * Get media for business listing
     *
     * @param int $post_id Post ID.
     * @return array Media items.
     */
    public function get_business_media( $post_id ) {
        $media = array();
        
        // Get gallery items
        $gallery = get_post_meta( $post_id, 'vmi_business_gallery', true );
        if ( ! empty( $gallery ) && is_array( $gallery ) ) {
            foreach ( $gallery as $item ) {
                $media[] = $this->format_media_item( $item );
            }
        }
        
        // Get featured tour
        $featured_tour = get_post_meta( $post_id, 'vmi_featured_tour', true );
        if ( ! empty( $featured_tour ) ) {
            $media[] = $this->format_media_item( $featured_tour, 'featured_tour' );
        }
        
        // Get 3D showcase
        $showcase = get_post_meta( $post_id, 'vmi_3d_showcase', true );
        if ( ! empty( $showcase ) ) {
            $media[] = $this->format_media_item( $showcase, '3d_showcase' );
        }
        
        return $media;
    }

    /**
     * Format media item for display
     *
     * @param mixed  $item Media item data.
     * @param string $type Media type.
     * @return array Formatted media item.
     */
    private function format_media_item( $item, $type = 'gallery' ) {
        if ( is_numeric( $item ) ) {
            // Item is a post ID
            $post = get_post( $item );
            if ( ! $post ) {
                return null;
            }
            
            return array(
                'id' => $post->ID,
                'type' => $post->post_type,
                'title' => $post->post_title,
                'url' => get_permalink( $post->ID ),
                'thumbnail' => get_the_post_thumbnail_url( $post->ID, 'medium' ),
                'meta' => $this->get_media_meta( $post->ID, $post->post_type ),
            );
        } elseif ( is_array( $item ) && isset( $item['id'] ) ) {
            // Item is an array with ID
            return $this->format_media_item( $item['id'], $type );
        }
        
        return null;
    }

    /**
     * Get media metadata
     *
     * @param int    $post_id Post ID.
     * @param string $post_type Post type.
     * @return array Media metadata.
     */
    private function get_media_meta( $post_id, $post_type ) {
        $meta = array();
        
        switch ( $post_type ) {
            case 'vmi_3d_models':
                $meta['format'] = get_post_meta( $post_id, 'vmi_model_format', true );
                $meta['file_url'] = get_post_meta( $post_id, 'vmi_model_file', true );
                $meta['size'] = get_post_meta( $post_id, 'vmi_model_size', true );
                break;
                
            case 'vmi_videos':
                $meta['video_url'] = get_post_meta( $post_id, 'vmi_video_url', true );
                $meta['video_type'] = get_post_meta( $post_id, 'vmi_video_type', true );
                $meta['duration'] = get_post_meta( $post_id, 'vmi_video_duration', true );
                break;
                
            case 'vmi_virtual_tours':
                $meta['tour_config'] = get_post_meta( $post_id, 'vmi_tour_config', true );
                $meta['tour_type'] = get_post_meta( $post_id, 'vmi_tour_type', true );
                break;
        }
        
        return $meta;
    }

    /**
     * Render media for timeline
     *
     * @param int $post_id Post ID.
     * @return string HTML output.
     */
    public function render_timeline_media( $post_id ) {
        $post_type = get_post_type( $post_id );
        
        switch ( $post_type ) {
            case 'vmi_3d_models':
                return do_shortcode( '[vmi_3d_model id="' . $post_id . '" width="100%" height="300px"]' );
                
            case 'vmi_videos':
                return do_shortcode( '[vmi_video id="' . $post_id . '" width="100%" height="300px"]' );
                
            case 'vmi_virtual_tours':
                return do_shortcode( '[vmi_virtual_tour id="' . $post_id . '" width="100%" height="300px"]' );
                
            default:
                return '';
        }
    }
}
