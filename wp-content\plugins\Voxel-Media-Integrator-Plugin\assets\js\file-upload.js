/**
 * File Upload JavaScript for VMI
 */

(function($) {
    'use strict';

    class VMIFileUploader {
        constructor() {
            this.init();
        }

        init() {
            this.bindEvents();
            this.setupDropzones();
        }

        bindEvents() {
            // Upload button clicks
            $(document).on('click', '.vmi-upload-button', this.handleUploadButtonClick.bind(this));
            
            // File input changes
            $(document).on('change', '.vmi-file-input', this.handleFileInputChange.bind(this));
            
            // Delete file buttons
            $(document).on('click', '.vmi-delete-file', this.handleFileDelete.bind(this));
            
            // Drag and drop events
            $(document).on('dragover dragenter', '.vmi-upload-dropzone', this.handleDragOver.bind(this));
            $(document).on('dragleave', '.vmi-upload-dropzone', this.handleDragLeave.bind(this));
            $(document).on('drop', '.vmi-upload-dropzone', this.handleDrop.bind(this));
        }

        setupDropzones() {
            $('.vmi-upload-dropzone').each(function() {
                $(this).addClass('vmi-dropzone-ready');
            });
        }

        handleUploadButtonClick(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);
            const $container = $button.closest('.vmi-upload-area');
            const $fileInput = $container.find('.vmi-file-input');
            
            $fileInput.trigger('click');
        }

        handleFileInputChange(e) {
            const files = e.target.files;
            if (files.length > 0) {
                const $container = $(e.target).closest('.vmi-upload-area');
                this.uploadFile(files[0], $container);
            }
        }

        handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            $(e.currentTarget).addClass('vmi-drag-over');
        }

        handleDragLeave(e) {
            e.preventDefault();
            e.stopPropagation();
            $(e.currentTarget).removeClass('vmi-drag-over');
        }

        handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const $dropzone = $(e.currentTarget);
            $dropzone.removeClass('vmi-drag-over');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                const $container = $dropzone.closest('.vmi-upload-area');
                this.uploadFile(files[0], $container);
            }
        }

        uploadFile(file, $container) {
            const uploadType = $container.data('upload-type');
            const postId = $container.data('post-id');
            
            // Validate file
            if (!this.validateFile(file, uploadType)) {
                return;
            }

            // Show progress
            this.showProgress($container);
            
            // Prepare form data
            const formData = new FormData();
            formData.append('action', `vmi_upload_${uploadType}`);
            formData.append('file', file);
            formData.append('post_id', postId);
            formData.append('nonce', vmi_upload.nonce);

            // Upload file
            $.ajax({
                url: vmi_upload.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: () => {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', (e) => {
                        if (e.lengthComputable) {
                            const percentComplete = (e.loaded / e.total) * 100;
                            this.updateProgress($container, percentComplete);
                        }
                    });
                    return xhr;
                },
                success: (response) => {
                    this.handleUploadSuccess(response, $container);
                },
                error: (xhr, status, error) => {
                    this.handleUploadError(error, $container);
                }
            });
        }

        validateFile(file, uploadType) {
            // Check file size
            if (file.size > vmi_upload.max_file_size) {
                this.showError(vmi_upload.strings.file_too_large);
                return false;
            }

            // Check file format
            const fileName = file.name.toLowerCase();
            const extension = fileName.split('.').pop();
            
            let allowedFormats = [];
            if (uploadType === '3d_model') {
                allowedFormats = vmi_upload.allowed_3d_formats;
            } else if (uploadType === 'video') {
                allowedFormats = vmi_upload.allowed_video_formats;
            } else if (uploadType === 'image') {
                allowedFormats = vmi_upload.allowed_image_formats;
            }

            if (!allowedFormats.includes(extension)) {
                this.showError(vmi_upload.strings.invalid_format);
                return false;
            }

            return true;
        }

        showProgress($container) {
            $container.find('.vmi-upload-dropzone').hide();
            $container.find('.vmi-upload-progress').show();
            this.updateProgress($container, 0);
        }

        updateProgress($container, percent) {
            const $progressFill = $container.find('.vmi-progress-fill');
            const $progressText = $container.find('.vmi-progress-text');
            
            $progressFill.css('width', percent + '%');
            $progressText.text(Math.round(percent) + '%');
        }

        hideProgress($container) {
            $container.find('.vmi-upload-progress').hide();
            $container.find('.vmi-upload-dropzone').show();
        }

        handleUploadSuccess(response, $container) {
            this.hideProgress($container);
            
            if (response.success) {
                this.showSuccess(response.data.message);
                this.updateFileDisplay($container, response.data);
                
                // Refresh the page to show updated file info
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                this.showError(response.data || vmi_upload.strings.upload_error);
            }
        }

        handleUploadError(error, $container) {
            this.hideProgress($container);
            this.showError(vmi_upload.strings.upload_error);
            console.error('Upload error:', error);
        }

        updateFileDisplay($container, fileData) {
            const $section = $container.closest('.vmi-upload-section');
            
            // Create or update current file display
            let $currentFile = $section.find('.vmi-current-file');
            if ($currentFile.length === 0) {
                $currentFile = $('<div class="vmi-current-file"></div>');
                $container.before($currentFile);
            }

            $currentFile.html(`
                <p><strong>Current file:</strong></p>
                <p><a href="${fileData.url}" target="_blank">${fileData.filename}</a></p>
                <p><em>${fileData.format} format - ${fileData.size}</em></p>
                <button type="button" class="button vmi-delete-file" data-file-type="${$container.data('upload-type').replace('_', '')}" data-post-id="${$container.data('post-id')}">
                    Remove File
                </button>
            `);
        }

        handleFileDelete(e) {
            e.preventDefault();
            
            if (!confirm(vmi_upload.strings.delete_confirm)) {
                return;
            }

            const $button = $(e.currentTarget);
            const fileType = $button.data('file-type');
            const postId = $button.data('post-id');

            $button.prop('disabled', true).text('Removing...');

            $.ajax({
                url: vmi_upload.ajax_url,
                type: 'POST',
                data: {
                    action: 'vmi_delete_media_file',
                    file_type: fileType,
                    post_id: postId,
                    nonce: vmi_upload.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.showSuccess(response.data.message);
                        $button.closest('.vmi-current-file').remove();
                    } else {
                        this.showError(response.data || 'Delete failed');
                        $button.prop('disabled', false).text('Remove File');
                    }
                },
                error: () => {
                    this.showError('Delete failed');
                    $button.prop('disabled', false).text('Remove File');
                }
            });
        }

        showSuccess(message) {
            this.showNotice(message, 'success');
        }

        showError(message) {
            this.showNotice(message, 'error');
        }

        showNotice(message, type) {
            // Remove existing notices
            $('.vmi-upload-notice').remove();
            
            // Create notice
            const $notice = $(`
                <div class="vmi-upload-notice vmi-notice-${type}">
                    <p>${message}</p>
                    <button type="button" class="notice-dismiss">
                        <span class="screen-reader-text">Dismiss this notice.</span>
                    </button>
                </div>
            `);

            // Add to page
            if ($('.vmi-upload-container').length > 0) {
                $('.vmi-upload-container').prepend($notice);
            } else {
                $('#wpbody-content .wrap').prepend($notice);
            }

            // Auto-dismiss success notices
            if (type === 'success') {
                setTimeout(() => {
                    $notice.fadeOut(() => $notice.remove());
                }, 3000);
            }

            // Handle dismiss button
            $notice.find('.notice-dismiss').on('click', () => {
                $notice.fadeOut(() => $notice.remove());
            });
        }
    }

    // Initialize when document is ready
    $(document).ready(() => {
        new VMIFileUploader();
    });

    // Also initialize on post editor load (for Gutenberg)
    $(window).on('load', () => {
        // Small delay to ensure all elements are loaded
        setTimeout(() => {
            if ($('.vmi-upload-container').length > 0) {
                new VMIFileUploader();
            }
        }, 500);
    });

})(jQuery);
