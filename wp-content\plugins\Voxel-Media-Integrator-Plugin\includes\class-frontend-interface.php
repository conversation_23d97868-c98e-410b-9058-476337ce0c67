<?php
/**
 * Frontend User Interface for Media Management
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class for frontend user interface
 */
class VMI_Frontend_Interface {

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', array( $this, 'init' ) );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_frontend_scripts' ) );
        add_shortcode( 'vmi_user_dashboard', array( $this, 'render_user_dashboard' ) );
        add_shortcode( 'vmi_upload_form', array( $this, 'render_upload_form' ) );

        // AJAX handlers for frontend
        add_action( 'wp_ajax_vmi_frontend_upload', array( $this, 'handle_frontend_upload' ) );
        add_action( 'wp_ajax_vmi_frontend_delete', array( $this, 'handle_frontend_delete' ) );
        add_action( 'wp_ajax_vmi_frontend_update', array( $this, 'handle_frontend_update' ) );
        add_action( 'wp_ajax_vmi_get_user_media', array( $this, 'get_user_media' ) );
    }

    /**
     * Initialize frontend interface
     */
    public function init() {
        // Add rewrite rules for user dashboard
        add_rewrite_rule( '^media-dashboard/?$', 'index.php?vmi_page=dashboard', 'top' );
        add_rewrite_rule( '^media-dashboard/([^/]+)/?$', 'index.php?vmi_page=dashboard&vmi_section=$matches[1]', 'top' );

        // Add query vars
        add_filter( 'query_vars', array( $this, 'add_query_vars' ) );

        // Handle custom pages
        add_action( 'template_redirect', array( $this, 'handle_custom_pages' ) );
    }

    /**
     * Add query variables
     *
     * @param array $vars Query variables.
     * @return array Modified query variables.
     */
    public function add_query_vars( $vars ) {
        $vars[] = 'vmi_page';
        $vars[] = 'vmi_section';
        return $vars;
    }

    /**
     * Handle custom pages
     */
    public function handle_custom_pages() {
        $vmi_page = get_query_var( 'vmi_page' );

        if ( $vmi_page === 'dashboard' ) {
            $this->render_dashboard_page();
            exit;
        }
    }

    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        if ( is_user_logged_in() ) {
            wp_enqueue_script( 'vmi-frontend', VMI_PLUGIN_URL . 'assets/js/frontend-interface.js', array( 'jquery' ), VMI_VERSION, true );
            wp_enqueue_style( 'vmi-frontend', VMI_PLUGIN_URL . 'assets/css/frontend-interface.css', array(), VMI_VERSION );

            wp_localize_script( 'vmi-frontend', 'vmi_frontend', array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'vmi_frontend_nonce' ),
                'user_id' => get_current_user_id(),
                'strings' => array(
                    'confirm_delete' => __( 'Are you sure you want to delete this item?', 'voxel-media-integrator' ),
                    'upload_success' => __( 'Upload successful!', 'voxel-media-integrator' ),
                    'upload_error' => __( 'Upload failed. Please try again.', 'voxel-media-integrator' ),
                    'loading' => __( 'Loading...', 'voxel-media-integrator' ),
                    'no_media' => __( 'No media found.', 'voxel-media-integrator' ),
                ),
            ) );
        }
    }

    /**
     * Render user dashboard shortcode
     *
     * @param array $atts Shortcode attributes.
     * @return string HTML output.
     */
    public function render_user_dashboard( $atts ) {
        if ( ! is_user_logged_in() ) {
            return '<p>' . __( 'Please log in to access your media dashboard.', 'voxel-media-integrator' ) . '</p>';
        }

        $atts = shortcode_atts( array(
            'show_upload' => 'true',
            'show_stats' => 'true',
            'items_per_page' => '12',
            'default_view' => 'grid', // grid, list
        ), $atts, 'vmi_user_dashboard' );

        ob_start();
        $this->render_dashboard_content( $atts );
        return ob_get_clean();
    }

    /**
     * Render upload form shortcode
     *
     * @param array $atts Shortcode attributes.
     * @return string HTML output.
     */
    public function render_upload_form( $atts ) {
        if ( ! is_user_logged_in() ) {
            return '<p>' . __( 'Please log in to upload media.', 'voxel-media-integrator' ) . '</p>';
        }

        $atts = shortcode_atts( array(
            'type' => 'all', // all, 3d_models, videos, virtual_tours
            'redirect' => '',
            'show_preview' => 'true',
        ), $atts, 'vmi_upload_form' );

        ob_start();
        $this->render_upload_form_content( $atts );
        return ob_get_clean();
    }

    /**
     * Render dashboard page
     */
    private function render_dashboard_page() {
        if ( ! is_user_logged_in() ) {
            wp_redirect( wp_login_url( home_url( '/media-dashboard/' ) ) );
            exit;
        }

        get_header();

        echo '<div class="vmi-dashboard-page">';
        echo '<div class="container">';

        $section = get_query_var( 'vmi_section', 'overview' );
        $this->render_dashboard_navigation( $section );
        $this->render_dashboard_section( $section );

        echo '</div>';
        echo '</div>';

        get_footer();
    }

    /**
     * Render dashboard navigation
     *
     * @param string $current_section Current section.
     */
    private function render_dashboard_navigation( $current_section ) {
        $sections = array(
            'overview' => array(
                'label' => __( 'Overview', 'voxel-media-integrator' ),
                'icon' => 'dashicons-dashboard',
            ),
            '3d-models' => array(
                'label' => __( '3D Models', 'voxel-media-integrator' ),
                'icon' => 'dashicons-media-interactive',
            ),
            'videos' => array(
                'label' => __( 'Videos', 'voxel-media-integrator' ),
                'icon' => 'dashicons-video-alt3',
            ),
            'virtual-tours' => array(
                'label' => __( 'Virtual Tours', 'voxel-media-integrator' ),
                'icon' => 'dashicons-visibility',
            ),
            'upload' => array(
                'label' => __( 'Upload', 'voxel-media-integrator' ),
                'icon' => 'dashicons-upload',
            ),
            'settings' => array(
                'label' => __( 'Settings', 'voxel-media-integrator' ),
                'icon' => 'dashicons-admin-settings',
            ),
        );

        echo '<nav class="vmi-dashboard-nav">';
        echo '<ul class="vmi-nav-tabs">';

        foreach ( $sections as $key => $section ) {
            $active_class = $current_section === $key ? ' active' : '';
            $url = home_url( '/media-dashboard/' . $key . '/' );

            echo '<li class="vmi-nav-item' . $active_class . '">';
            echo '<a href="' . esc_url( $url ) . '" class="vmi-nav-link">';
            echo '<span class="dashicons ' . esc_attr( $section['icon'] ) . '"></span>';
            echo '<span class="vmi-nav-label">' . esc_html( $section['label'] ) . '</span>';
            echo '</a>';
            echo '</li>';
        }

        echo '</ul>';
        echo '</nav>';
    }

    /**
     * Render dashboard section
     *
     * @param string $section Section to render.
     */
    private function render_dashboard_section( $section ) {
        echo '<div class="vmi-dashboard-content">';

        switch ( $section ) {
            case 'overview':
                $this->render_overview_section();
                break;
            case '3d-models':
                $this->render_media_section( 'vmi_3d_models' );
                break;
            case 'videos':
                $this->render_media_section( 'vmi_videos' );
                break;
            case 'virtual-tours':
                $this->render_media_section( 'vmi_virtual_tours' );
                break;
            case 'upload':
                $this->render_upload_section();
                break;
            case 'settings':
                $this->render_settings_section();
                break;
            default:
                $this->render_overview_section();
                break;
        }

        echo '</div>';
    }

    /**
     * Render overview section
     */
    private function render_overview_section() {
        $user_id = get_current_user_id();

        // Get user media counts
        $models_count = $this->get_user_media_count( $user_id, 'vmi_3d_models' );
        $videos_count = $this->get_user_media_count( $user_id, 'vmi_videos' );
        $tours_count = $this->get_user_media_count( $user_id, 'vmi_virtual_tours' );
        $total_count = $models_count + $videos_count + $tours_count;

        echo '<div class="vmi-overview-section">';
        echo '<h2>' . __( 'Media Overview', 'voxel-media-integrator' ) . '</h2>';

        // Stats cards
        echo '<div class="vmi-stats-grid">';

        $stats = array(
            array(
                'label' => __( 'Total Media', 'voxel-media-integrator' ),
                'count' => $total_count,
                'icon' => 'dashicons-portfolio',
                'color' => '#3498db',
            ),
            array(
                'label' => __( '3D Models', 'voxel-media-integrator' ),
                'count' => $models_count,
                'icon' => 'dashicons-media-interactive',
                'color' => '#e74c3c',
            ),
            array(
                'label' => __( 'Videos', 'voxel-media-integrator' ),
                'count' => $videos_count,
                'icon' => 'dashicons-video-alt3',
                'color' => '#2ecc71',
            ),
            array(
                'label' => __( 'Virtual Tours', 'voxel-media-integrator' ),
                'count' => $tours_count,
                'icon' => 'dashicons-visibility',
                'color' => '#f39c12',
            ),
        );

        foreach ( $stats as $stat ) {
            echo '<div class="vmi-stat-card" style="border-left-color: ' . esc_attr( $stat['color'] ) . ';">';
            echo '<div class="vmi-stat-icon" style="color: ' . esc_attr( $stat['color'] ) . ';">';
            echo '<span class="dashicons ' . esc_attr( $stat['icon'] ) . '"></span>';
            echo '</div>';
            echo '<div class="vmi-stat-content">';
            echo '<div class="vmi-stat-number">' . esc_html( $stat['count'] ) . '</div>';
            echo '<div class="vmi-stat-label">' . esc_html( $stat['label'] ) . '</div>';
            echo '</div>';
            echo '</div>';
        }

        echo '</div>';

        // Recent media
        echo '<div class="vmi-recent-media">';
        echo '<h3>' . __( 'Recent Media', 'voxel-media-integrator' ) . '</h3>';

        $recent_media = $this->get_user_recent_media( $user_id, 6 );
        if ( ! empty( $recent_media ) ) {
            echo '<div class="vmi-media-grid">';
            foreach ( $recent_media as $media ) {
                $this->render_media_card( $media );
            }
            echo '</div>';
        } else {
            echo '<p class="vmi-no-media">' . __( 'No media found. Start by uploading your first item!', 'voxel-media-integrator' ) . '</p>';
            echo '<a href="' . esc_url( home_url( '/media-dashboard/upload/' ) ) . '" class="vmi-btn vmi-btn-primary">';
            echo __( 'Upload Media', 'voxel-media-integrator' ) . '</a>';
        }

        echo '</div>';
        echo '</div>';
    }

    /**
     * Render media section
     *
     * @param string $post_type Post type to display.
     */
    private function render_media_section( $post_type ) {
        $user_id = get_current_user_id();
        $page = isset( $_GET['paged'] ) ? max( 1, intval( $_GET['paged'] ) ) : 1;
        $per_page = 12;

        $media_query = new WP_Query( array(
            'post_type' => $post_type,
            'author' => $user_id,
            'posts_per_page' => $per_page,
            'paged' => $page,
            'post_status' => array( 'publish', 'draft', 'pending' ),
            'orderby' => 'date',
            'order' => 'DESC',
        ) );

        $type_labels = array(
            'vmi_3d_models' => __( '3D Models', 'voxel-media-integrator' ),
            'vmi_videos' => __( 'Videos', 'voxel-media-integrator' ),
            'vmi_virtual_tours' => __( 'Virtual Tours', 'voxel-media-integrator' ),
        );

        echo '<div class="vmi-media-section">';
        echo '<div class="vmi-section-header">';
        echo '<h2>' . esc_html( $type_labels[ $post_type ] ?? 'Media' ) . '</h2>';
        echo '<a href="' . esc_url( home_url( '/media-dashboard/upload/' ) ) . '" class="vmi-btn vmi-btn-primary">';
        echo __( 'Add New', 'voxel-media-integrator' ) . '</a>';
        echo '</div>';

        if ( $media_query->have_posts() ) {
            echo '<div class="vmi-media-grid">';
            while ( $media_query->have_posts() ) {
                $media_query->the_post();
                $this->render_media_card( get_post() );
            }
            echo '</div>';

            // Pagination
            if ( $media_query->max_num_pages > 1 ) {
                echo '<div class="vmi-pagination">';
                echo paginate_links( array(
                    'total' => $media_query->max_num_pages,
                    'current' => $page,
                    'format' => '?paged=%#%',
                    'prev_text' => '&laquo; ' . __( 'Previous', 'voxel-media-integrator' ),
                    'next_text' => __( 'Next', 'voxel-media-integrator' ) . ' &raquo;',
                ) );
                echo '</div>';
            }
        } else {
            echo '<div class="vmi-no-media">';
            echo '<p>' . __( 'No media found.', 'voxel-media-integrator' ) . '</p>';
            echo '<a href="' . esc_url( home_url( '/media-dashboard/upload/' ) ) . '" class="vmi-btn vmi-btn-primary">';
            echo __( 'Upload Your First Item', 'voxel-media-integrator' ) . '</a>';
            echo '</div>';
        }

        wp_reset_postdata();
        echo '</div>';
    }

    /**
     * Render upload section
     */
    private function render_upload_section() {
        echo '<div class="vmi-upload-section">';
        echo '<h2>' . __( 'Upload Media', 'voxel-media-integrator' ) . '</h2>';

        echo '<div class="vmi-upload-tabs">';
        echo '<button class="vmi-tab-btn active" data-tab="3d-models">' . __( '3D Models', 'voxel-media-integrator' ) . '</button>';
        echo '<button class="vmi-tab-btn" data-tab="videos">' . __( 'Videos', 'voxel-media-integrator' ) . '</button>';
        echo '<button class="vmi-tab-btn" data-tab="virtual-tours">' . __( 'Virtual Tours', 'voxel-media-integrator' ) . '</button>';
        echo '</div>';

        echo '<div class="vmi-upload-forms">';

        // 3D Models upload form
        echo '<div class="vmi-upload-form active" data-form="3d-models">';
        $this->render_upload_form_content( array( 'type' => '3d_models' ) );
        echo '</div>';

        // Videos upload form
        echo '<div class="vmi-upload-form" data-form="videos">';
        $this->render_upload_form_content( array( 'type' => 'videos' ) );
        echo '</div>';

        // Virtual Tours upload form
        echo '<div class="vmi-upload-form" data-form="virtual-tours">';
        $this->render_upload_form_content( array( 'type' => 'virtual_tours' ) );
        echo '</div>';

        echo '</div>';
        echo '</div>';
    }

    /**
     * Get user media count
     *
     * @param int    $user_id User ID.
     * @param string $post_type Post type.
     * @return int Media count.
     */
    private function get_user_media_count( $user_id, $post_type ) {
        $count_query = new WP_Query( array(
            'post_type' => $post_type,
            'author' => $user_id,
            'posts_per_page' => -1,
            'post_status' => array( 'publish', 'draft', 'pending' ),
            'fields' => 'ids',
        ) );

        return $count_query->found_posts;
    }

    /**
     * Get user recent media
     *
     * @param int $user_id User ID.
     * @param int $limit Number of items to retrieve.
     * @return array Recent media posts.
     */
    private function get_user_recent_media( $user_id, $limit = 6 ) {
        $recent_query = new WP_Query( array(
            'post_type' => array( 'vmi_3d_models', 'vmi_videos', 'vmi_virtual_tours' ),
            'author' => $user_id,
            'posts_per_page' => $limit,
            'post_status' => array( 'publish', 'draft', 'pending' ),
            'orderby' => 'date',
            'order' => 'DESC',
        ) );

        return $recent_query->posts;
    }

    /**
     * Render media card
     *
     * @param WP_Post $post Media post.
     */
    private function render_media_card( $post ) {
        $post_type = $post->post_type;
        $type_labels = array(
            'vmi_3d_models' => __( '3D Model', 'voxel-media-integrator' ),
            'vmi_videos' => __( 'Video', 'voxel-media-integrator' ),
            'vmi_virtual_tours' => __( 'Virtual Tour', 'voxel-media-integrator' ),
        );

        echo '<div class="vmi-media-card" data-post-id="' . esc_attr( $post->ID ) . '">';

        // Thumbnail
        if ( has_post_thumbnail( $post->ID ) ) {
            echo '<div class="vmi-card-thumbnail">';
            echo get_the_post_thumbnail( $post->ID, 'medium' );
            echo '</div>';
        } else {
            echo '<div class="vmi-card-thumbnail vmi-no-thumbnail">';
            echo '<span class="vmi-media-icon">📁</span>';
            echo '</div>';
        }

        // Content
        echo '<div class="vmi-card-content">';
        echo '<h4 class="vmi-card-title">' . esc_html( $post->post_title ) . '</h4>';
        echo '<p class="vmi-card-type">' . esc_html( $type_labels[ $post_type ] ?? 'Media' ) . '</p>';
        echo '<p class="vmi-card-date">' . esc_html( get_the_date( '', $post->ID ) ) . '</p>';
        echo '<div class="vmi-card-status">';
        echo '<span class="vmi-status vmi-status-' . esc_attr( $post->post_status ) . '">';
        echo esc_html( ucfirst( $post->post_status ) );
        echo '</span>';
        echo '</div>';
        echo '</div>';

        // Actions
        echo '<div class="vmi-card-actions">';
        echo '<a href="' . esc_url( get_permalink( $post->ID ) ) . '" class="vmi-btn vmi-btn-sm">' . __( 'View', 'voxel-media-integrator' ) . '</a>';
        echo '<a href="' . esc_url( get_edit_post_link( $post->ID ) ) . '" class="vmi-btn vmi-btn-sm">' . __( 'Edit', 'voxel-media-integrator' ) . '</a>';
        echo '<button class="vmi-btn vmi-btn-sm vmi-btn-danger vmi-delete-media" data-post-id="' . esc_attr( $post->ID ) . '">' . __( 'Delete', 'voxel-media-integrator' ) . '</button>';
        echo '</div>';

        echo '</div>';
    }

    /**
     * Render upload form content
     *
     * @param array $atts Form attributes.
     */
    private function render_upload_form_content( $atts ) {
        echo '<div class="vmi-upload-form-content">';
        echo '<p>' . __( 'Upload form will be implemented here.', 'voxel-media-integrator' ) . '</p>';
        echo '</div>';
    }

    /**
     * Render dashboard content
     *
     * @param array $atts Dashboard attributes.
     */
    private function render_dashboard_content( $atts ) {
        echo '<div class="vmi-dashboard-content">';
        echo '<p>' . __( 'Dashboard content will be implemented here.', 'voxel-media-integrator' ) . '</p>';
        echo '</div>';
    }

    /**
     * Render settings section
     */
    private function render_settings_section() {
        echo '<div class="vmi-settings-section">';
        echo '<h2>' . __( 'Settings', 'voxel-media-integrator' ) . '</h2>';
        echo '<p>' . __( 'Settings will be implemented here.', 'voxel-media-integrator' ) . '</p>';
        echo '</div>';
    }

    /**
     * Handle frontend upload
     */
    public function handle_frontend_upload() {
        // Placeholder for frontend upload handler
        wp_send_json_error( __( 'Frontend upload not yet implemented.', 'voxel-media-integrator' ) );
    }

    /**
     * Handle frontend delete
     */
    public function handle_frontend_delete() {
        // Placeholder for frontend delete handler
        wp_send_json_error( __( 'Frontend delete not yet implemented.', 'voxel-media-integrator' ) );
    }

    /**
     * Handle frontend update
     */
    public function handle_frontend_update() {
        // Placeholder for frontend update handler
        wp_send_json_error( __( 'Frontend update not yet implemented.', 'voxel-media-integrator' ) );
    }

    /**
     * Get user media
     */
    public function get_user_media() {
        // Placeholder for get user media handler
        wp_send_json_error( __( 'Get user media not yet implemented.', 'voxel-media-integrator' ) );
    }
}