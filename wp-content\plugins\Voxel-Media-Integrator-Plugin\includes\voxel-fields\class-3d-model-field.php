<?php
/**
 * Voxel 3D Model Field Type
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * 3D Model field type for Voxel
 */
class VMI_Voxel_3D_Model_Field extends \Voxel\Field_Types\Base_Field_Type {

    protected $props = array(
        'type' => 'vmi_3d_model',
        'label' => '3D Model',
        'placeholder' => '',
        'description' => '',
        'required' => false,
        'display_mode' => 'viewer', // viewer, link, thumbnail
        'auto_rotate' => true,
        'camera_controls' => true,
        'environment_image' => '',
        'width' => '100%',
        'height' => '400px',
        'allowed_formats' => array( 'glb', 'gltf', 'obj', 'fbx' ),
        'max_file_size' => 50, // MB
    );

    /**
     * Get field configuration
     *
     * @return array Field configuration.
     */
    public function get_models() {
        return array(
            'label' => $this->get_label(),
            'type' => 'vmi_3d_model',
            'props' => array(
                'label' => array(
                    'type' => 'text',
                    'label' => 'Label',
                    'width' => '1/1',
                ),
                'placeholder' => array(
                    'type' => 'text',
                    'label' => 'Placeholder',
                    'width' => '1/2',
                ),
                'description' => array(
                    'type' => 'textarea',
                    'label' => 'Description',
                    'width' => '1/1',
                ),
                'required' => array(
                    'type' => 'switcher',
                    'label' => 'Required',
                    'width' => '1/3',
                ),
                'display_mode' => array(
                    'type' => 'select',
                    'label' => 'Display Mode',
                    'width' => '1/3',
                    'choices' => array(
                        'viewer' => 'Interactive Viewer',
                        'link' => 'Download Link',
                        'thumbnail' => 'Thumbnail Only',
                    ),
                ),
                'auto_rotate' => array(
                    'type' => 'switcher',
                    'label' => 'Auto Rotate',
                    'width' => '1/3',
                    'condition' => array(
                        'display_mode' => 'viewer',
                    ),
                ),
                'camera_controls' => array(
                    'type' => 'switcher',
                    'label' => 'Camera Controls',
                    'width' => '1/3',
                    'condition' => array(
                        'display_mode' => 'viewer',
                    ),
                ),
                'width' => array(
                    'type' => 'text',
                    'label' => 'Width',
                    'width' => '1/3',
                    'condition' => array(
                        'display_mode' => 'viewer',
                    ),
                ),
                'height' => array(
                    'type' => 'text',
                    'label' => 'Height',
                    'width' => '1/3',
                    'condition' => array(
                        'display_mode' => 'viewer',
                    ),
                ),
                'allowed_formats' => array(
                    'type' => 'checkboxes',
                    'label' => 'Allowed Formats',
                    'width' => '1/2',
                    'choices' => array(
                        'glb' => 'GLB',
                        'gltf' => 'GLTF',
                        'obj' => 'OBJ',
                        'fbx' => 'FBX',
                        'dae' => 'DAE',
                        'stl' => 'STL',
                    ),
                ),
                'max_file_size' => array(
                    'type' => 'number',
                    'label' => 'Max File Size (MB)',
                    'width' => '1/2',
                    'min' => 1,
                    'max' => 500,
                ),
            ),
        );
    }

    /**
     * Sanitize field value
     *
     * @param mixed $value Field value.
     * @return mixed Sanitized value.
     */
    public function sanitize( $value ) {
        if ( is_numeric( $value ) ) {
            $post = get_post( $value );
            if ( $post && $post->post_type === 'vmi_3d_models' ) {
                return absint( $value );
            }
        }
        
        return null;
    }

    /**
     * Validate field value
     *
     * @param mixed $value Field value.
     * @return bool|string True if valid, error message if invalid.
     */
    public function validate( $value ) {
        if ( $this->props['required'] && empty( $value ) ) {
            return sprintf( 'Field "%s" is required.', $this->get_label() );
        }

        if ( ! empty( $value ) ) {
            $post = get_post( $value );
            if ( ! $post || $post->post_type !== 'vmi_3d_models' ) {
                return sprintf( 'Invalid 3D model selected for field "%s".', $this->get_label() );
            }

            // Check file format if specified
            if ( ! empty( $this->props['allowed_formats'] ) ) {
                $format = get_post_meta( $post->ID, 'vmi_model_format', true );
                if ( $format && ! in_array( $format, $this->props['allowed_formats'] ) ) {
                    return sprintf( 'Invalid file format for field "%s". Allowed formats: %s', 
                        $this->get_label(), 
                        implode( ', ', $this->props['allowed_formats'] ) 
                    );
                }
            }

            // Check file size if specified
            if ( ! empty( $this->props['max_file_size'] ) ) {
                $size = get_post_meta( $post->ID, 'vmi_model_size', true );
                $max_size = $this->props['max_file_size'] * 1024 * 1024; // Convert MB to bytes
                if ( $size && $size > $max_size ) {
                    return sprintf( 'File too large for field "%s". Maximum size: %s MB', 
                        $this->get_label(), 
                        $this->props['max_file_size'] 
                    );
                }
            }
        }

        return true;
    }

    /**
     * Render field for frontend display
     *
     * @param mixed $value Field value.
     * @return string HTML output.
     */
    public function render( $value ) {
        if ( empty( $value ) ) {
            return '';
        }

        $post = get_post( $value );
        if ( ! $post || $post->post_type !== 'vmi_3d_models' ) {
            return '';
        }

        $display_mode = $this->props['display_mode'] ?? 'viewer';

        switch ( $display_mode ) {
            case 'viewer':
                return $this->render_viewer( $post );
            case 'link':
                return $this->render_link( $post );
            case 'thumbnail':
                return $this->render_thumbnail( $post );
            default:
                return $this->render_viewer( $post );
        }
    }

    /**
     * Render interactive 3D viewer
     *
     * @param WP_Post $post 3D model post.
     * @return string HTML output.
     */
    private function render_viewer( $post ) {
        $auto_rotate = $this->props['auto_rotate'] ? 'true' : 'false';
        $camera_controls = $this->props['camera_controls'] ? 'true' : 'false';
        $width = $this->props['width'] ?? '100%';
        $height = $this->props['height'] ?? '400px';

        $shortcode_atts = array(
            'id' => $post->ID,
            'width' => $width,
            'height' => $height,
            'auto_rotate' => $auto_rotate,
            'camera_controls' => $camera_controls,
        );

        if ( ! empty( $this->props['environment_image'] ) ) {
            $shortcode_atts['environment_image'] = $this->props['environment_image'];
        }

        $shortcode = '[vmi_3d_model';
        foreach ( $shortcode_atts as $key => $value ) {
            $shortcode .= ' ' . $key . '="' . esc_attr( $value ) . '"';
        }
        $shortcode .= ']';

        return do_shortcode( $shortcode );
    }

    /**
     * Render download link
     *
     * @param WP_Post $post 3D model post.
     * @return string HTML output.
     */
    private function render_link( $post ) {
        $model_file = get_post_meta( $post->ID, 'vmi_model_file', true );
        $model_format = get_post_meta( $post->ID, 'vmi_model_format', true );
        $model_size = get_post_meta( $post->ID, 'vmi_model_size', true );

        if ( empty( $model_file ) ) {
            return '';
        }

        $output = '<div class="vmi-3d-model-link">';
        $output .= '<a href="' . esc_url( $model_file ) . '" download class="vmi-download-link">';
        $output .= '<span class="vmi-download-icon">📦</span>';
        $output .= '<span class="vmi-download-text">';
        $output .= '<strong>' . esc_html( $post->post_title ) . '</strong>';
        if ( $model_format ) {
            $output .= '<br><small>' . esc_html( strtoupper( $model_format ) ) . ' format';
            if ( $model_size ) {
                $output .= ' • ' . size_format( $model_size );
            }
            $output .= '</small>';
        }
        $output .= '</span>';
        $output .= '</a>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render thumbnail
     *
     * @param WP_Post $post 3D model post.
     * @return string HTML output.
     */
    private function render_thumbnail( $post ) {
        $thumbnail = get_the_post_thumbnail( $post->ID, 'medium' );
        
        if ( empty( $thumbnail ) ) {
            $thumbnail = '<div class="vmi-no-thumbnail"><span class="vmi-3d-icon">📦</span></div>';
        }

        $output = '<div class="vmi-3d-model-thumbnail">';
        $output .= '<a href="' . esc_url( get_permalink( $post->ID ) ) . '">';
        $output .= $thumbnail;
        $output .= '<div class="vmi-thumbnail-overlay">';
        $output .= '<h4>' . esc_html( $post->post_title ) . '</h4>';
        $output .= '</div>';
        $output .= '</a>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render field for editing
     *
     * @param mixed $value Field value.
     * @return string HTML output.
     */
    public function render_edit_field( $value ) {
        $models = get_posts( array(
            'post_type' => 'vmi_3d_models',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC',
        ) );

        $output = '<div class="vmi-field-wrapper">';
        $output .= '<select name="' . esc_attr( $this->get_key() ) . '" class="vmi-3d-model-select">';
        $output .= '<option value="">' . esc_html( $this->props['placeholder'] ?: 'Select a 3D model...' ) . '</option>';

        foreach ( $models as $model ) {
            $selected = selected( $value, $model->ID, false );
            $format = get_post_meta( $model->ID, 'vmi_model_format', true );
            $format_text = $format ? ' (' . strtoupper( $format ) . ')' : '';
            
            $output .= '<option value="' . esc_attr( $model->ID ) . '"' . $selected . '>';
            $output .= esc_html( $model->post_title . $format_text );
            $output .= '</option>';
        }

        $output .= '</select>';
        
        if ( ! empty( $this->props['description'] ) ) {
            $output .= '<p class="vmi-field-description">' . esc_html( $this->props['description'] ) . '</p>';
        }
        
        $output .= '</div>';

        return $output;
    }

    /**
     * Get field value for export
     *
     * @param mixed $value Field value.
     * @return mixed Export value.
     */
    public function export_value( $value ) {
        if ( empty( $value ) ) {
            return null;
        }

        $post = get_post( $value );
        if ( ! $post || $post->post_type !== 'vmi_3d_models' ) {
            return null;
        }

        return array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'url' => get_permalink( $post->ID ),
            'file_url' => get_post_meta( $post->ID, 'vmi_model_file', true ),
            'format' => get_post_meta( $post->ID, 'vmi_model_format', true ),
            'size' => get_post_meta( $post->ID, 'vmi_model_size', true ),
            'thumbnail' => get_the_post_thumbnail_url( $post->ID, 'medium' ),
        );
    }
}
