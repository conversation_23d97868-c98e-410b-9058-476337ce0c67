<?php
/**
 * Template loader for custom post types
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class for loading custom templates
 */
class VMI_Template_Loader {

    /**
     * Constructor
     */
    public function __construct() {
        add_filter( 'template_include', array( $this, 'load_custom_templates' ) );
        add_filter( 'archive_template', array( $this, 'load_archive_templates' ) );
        add_filter( 'single_template', array( $this, 'load_single_templates' ) );
    }

    /**
     * Load custom templates for our post types
     *
     * @param string $template The template path.
     * @return string Modified template path.
     */
    public function load_custom_templates( $template ) {
        global $post;

        // Check if we're on a single post page for our custom post types
        if ( is_single() && isset( $post->post_type ) ) {
            $custom_template = $this->get_single_template( $post->post_type );
            if ( $custom_template ) {
                return $custom_template;
            }
        }

        // Check if we're on an archive page for our custom post types
        if ( is_post_type_archive() ) {
            $post_type = get_query_var( 'post_type' );
            $custom_template = $this->get_archive_template( $post_type );
            if ( $custom_template ) {
                return $custom_template;
            }
        }

        return $template;
    }

    /**
     * Load archive templates
     *
     * @param string $template The template path.
     * @return string Modified template path.
     */
    public function load_archive_templates( $template ) {
        $post_type = get_query_var( 'post_type' );
        $custom_template = $this->get_archive_template( $post_type );
        
        if ( $custom_template ) {
            return $custom_template;
        }

        return $template;
    }

    /**
     * Load single templates
     *
     * @param string $template The template path.
     * @return string Modified template path.
     */
    public function load_single_templates( $template ) {
        global $post;

        if ( isset( $post->post_type ) ) {
            $custom_template = $this->get_single_template( $post->post_type );
            if ( $custom_template ) {
                return $custom_template;
            }
        }

        return $template;
    }

    /**
     * Get single template for post type
     *
     * @param string $post_type The post type.
     * @return string|false Template path or false if not found.
     */
    private function get_single_template( $post_type ) {
        $vmi_post_types = array( 'vmi_3d_models', 'vmi_videos', 'vmi_virtual_tours', 'vmi_courses', 'vmi_lessons', 'vmi_quizzes' );
        
        if ( ! in_array( $post_type, $vmi_post_types ) ) {
            return false;
        }

        // Look for template in theme first
        $theme_template = locate_template( array(
            "single-{$post_type}.php",
            "vmi/single-{$post_type}.php",
            "voxel-media-integrator/single-{$post_type}.php"
        ) );

        if ( $theme_template ) {
            return $theme_template;
        }

        // Look for template in plugin
        $plugin_template = VMI_PLUGIN_DIR . "templates/single-{$post_type}.php";
        if ( file_exists( $plugin_template ) ) {
            return $plugin_template;
        }

        // Fallback to generic single template
        $generic_template = VMI_PLUGIN_DIR . "templates/single-vmi-media.php";
        if ( file_exists( $generic_template ) ) {
            return $generic_template;
        }

        return false;
    }

    /**
     * Get archive template for post type
     *
     * @param string $post_type The post type.
     * @return string|false Template path or false if not found.
     */
    private function get_archive_template( $post_type ) {
        $vmi_post_types = array( 'vmi_3d_models', 'vmi_videos', 'vmi_virtual_tours', 'vmi_courses', 'vmi_lessons', 'vmi_quizzes' );
        
        if ( ! in_array( $post_type, $vmi_post_types ) ) {
            return false;
        }

        // Look for template in theme first
        $theme_template = locate_template( array(
            "archive-{$post_type}.php",
            "vmi/archive-{$post_type}.php",
            "voxel-media-integrator/archive-{$post_type}.php"
        ) );

        if ( $theme_template ) {
            return $theme_template;
        }

        // Look for template in plugin
        $plugin_template = VMI_PLUGIN_DIR . "templates/archive-{$post_type}.php";
        if ( file_exists( $plugin_template ) ) {
            return $plugin_template;
        }

        // Fallback to generic archive template
        $generic_template = VMI_PLUGIN_DIR . "templates/archive-vmi-media.php";
        if ( file_exists( $generic_template ) ) {
            return $generic_template;
        }

        return false;
    }

    /**
     * Get template part
     *
     * @param string $slug Template slug.
     * @param string $name Template name.
     * @param array  $args Template arguments.
     */
    public static function get_template_part( $slug, $name = '', $args = array() ) {
        $template = '';

        // Look for template in theme first
        if ( $name ) {
            $template = locate_template( array(
                "vmi/{$slug}-{$name}.php",
                "voxel-media-integrator/{$slug}-{$name}.php",
                "{$slug}-{$name}.php"
            ) );
        }

        if ( ! $template ) {
            $template = locate_template( array(
                "vmi/{$slug}.php",
                "voxel-media-integrator/{$slug}.php",
                "{$slug}.php"
            ) );
        }

        // Look for template in plugin
        if ( ! $template ) {
            if ( $name ) {
                $plugin_template = VMI_PLUGIN_DIR . "templates/{$slug}-{$name}.php";
                if ( file_exists( $plugin_template ) ) {
                    $template = $plugin_template;
                }
            }

            if ( ! $template ) {
                $plugin_template = VMI_PLUGIN_DIR . "templates/{$slug}.php";
                if ( file_exists( $plugin_template ) ) {
                    $template = $plugin_template;
                }
            }
        }

        if ( $template ) {
            // Extract args to variables
            if ( ! empty( $args ) && is_array( $args ) ) {
                extract( $args );
            }

            include $template;
        }
    }

    /**
     * Load template with args
     *
     * @param string $template_name Template name.
     * @param array  $args Template arguments.
     * @param string $template_path Template path.
     * @param string $default_path Default path.
     * @return string Template content.
     */
    public static function load_template( $template_name, $args = array(), $template_path = '', $default_path = '' ) {
        if ( ! empty( $args ) && is_array( $args ) ) {
            extract( $args );
        }

        $located = self::locate_template( $template_name, $template_path, $default_path );

        if ( ! file_exists( $located ) ) {
            return '';
        }

        ob_start();
        include $located;
        return ob_get_clean();
    }

    /**
     * Locate template
     *
     * @param string $template_name Template name.
     * @param string $template_path Template path.
     * @param string $default_path Default path.
     * @return string Template location.
     */
    public static function locate_template( $template_name, $template_path = '', $default_path = '' ) {
        if ( ! $template_path ) {
            $template_path = 'vmi/';
        }

        if ( ! $default_path ) {
            $default_path = VMI_PLUGIN_DIR . 'templates/';
        }

        // Look within passed path within the theme - this is priority.
        $template = locate_template( array(
            trailingslashit( $template_path ) . $template_name,
            $template_name,
        ) );

        // Get default template/
        if ( ! $template ) {
            $template = $default_path . $template_name;
        }

        return $template;
    }

    /**
     * Get template content
     *
     * @param string $template_name Template name.
     * @param array  $args Template arguments.
     * @return string Template content.
     */
    public static function get_template_content( $template_name, $args = array() ) {
        ob_start();
        self::get_template_part( $template_name, '', $args );
        return ob_get_clean();
    }
}
