/**
 * File Upload Styles for VMI
 */

/* Upload Container */
.vmi-upload-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 15px;
}

.vmi-upload-section {
    margin-bottom: 20px;
}

.vmi-upload-section:last-child {
    margin-bottom: 0;
}

.vmi-upload-section h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 8px;
}

/* Current File Display */
.vmi-current-file {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.vmi-current-file p {
    margin: 0 0 8px 0;
    font-size: 13px;
}

.vmi-current-file p:last-child {
    margin-bottom: 0;
}

.vmi-current-file strong {
    color: #23282d;
}

.vmi-current-file a {
    color: #0073aa;
    text-decoration: none;
    word-break: break-all;
}

.vmi-current-file a:hover {
    text-decoration: underline;
}

.vmi-current-file em {
    color: #666;
    font-style: normal;
    font-size: 12px;
}

.vmi-current-file .button {
    margin-top: 10px;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

/* Upload Area */
.vmi-upload-area {
    position: relative;
}

.vmi-upload-dropzone {
    border: 2px dashed #c3c4c7;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.vmi-upload-dropzone:hover {
    border-color: #0073aa;
    background: #f0f6fc;
}

.vmi-upload-dropzone.vmi-drag-over {
    border-color: #00a32a;
    background: #f0f9f0;
    transform: scale(1.02);
}

.vmi-upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.7;
}

.vmi-upload-dropzone p {
    margin: 0 0 10px 0;
    color: #50575e;
    font-size: 14px;
}

.vmi-upload-dropzone p:last-child {
    margin-bottom: 0;
}

.vmi-upload-formats {
    color: #787c82 !important;
    font-size: 12px !important;
    margin-top: 5px !important;
}

.vmi-upload-button {
    margin-top: 15px;
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
    font-size: 13px;
    padding: 8px 16px;
    height: auto;
    line-height: 1.4;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.vmi-upload-button:hover {
    background: #005a87;
    border-color: #005a87;
    color: #fff;
}

/* Progress Bar */
.vmi-upload-progress {
    padding: 20px;
    text-align: center;
}

.vmi-progress-bar {
    width: 100%;
    height: 20px;
    background: #e1e1e1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
}

.vmi-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
    position: relative;
}

.vmi-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: vmi-progress-shine 2s infinite;
}

@keyframes vmi-progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.vmi-progress-text {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

/* Upload Notes */
.vmi-upload-note {
    margin-top: 15px;
    padding: 10px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
}

.vmi-upload-note p {
    margin: 0;
    color: #856404;
    font-size: 12px;
}

/* Notices */
.vmi-upload-notice {
    position: relative;
    margin: 0 0 15px 0;
    padding: 12px 15px;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.4;
}

.vmi-notice-success {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.vmi-notice-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.vmi-upload-notice p {
    margin: 0;
}

.vmi-upload-notice .notice-dismiss {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    line-height: 1;
    opacity: 0.7;
}

.vmi-upload-notice .notice-dismiss:hover {
    opacity: 1;
}

.vmi-upload-notice .notice-dismiss::before {
    content: '×';
    font-weight: bold;
}

.vmi-upload-notice .screen-reader-text {
    position: absolute !important;
    clip: rect(1px, 1px, 1px, 1px);
    word-wrap: normal !important;
    overflow: hidden;
}

/* Responsive Design */
@media screen and (max-width: 782px) {
    .vmi-upload-dropzone {
        padding: 20px 15px;
    }
    
    .vmi-upload-icon {
        font-size: 36px;
        margin-bottom: 10px;
    }
    
    .vmi-upload-dropzone p {
        font-size: 13px;
    }
    
    .vmi-upload-button {
        font-size: 12px;
        padding: 6px 12px;
    }
}

/* File Input Hidden */
.vmi-file-input {
    position: absolute;
    left: -9999px;
    opacity: 0;
    pointer-events: none;
}

/* Loading States */
.vmi-upload-dropzone.vmi-uploading {
    pointer-events: none;
    opacity: 0.7;
}

.vmi-upload-dropzone.vmi-uploading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e1e1e1;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: vmi-spin 1s linear infinite;
}

@keyframes vmi-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Delete Button States */
.vmi-delete-file:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Accessibility Improvements */
.vmi-upload-dropzone:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.vmi-upload-button:focus {
    box-shadow: 0 0 0 2px #0073aa;
    outline: none;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .vmi-upload-dropzone {
        border-width: 3px;
    }
    
    .vmi-upload-dropzone:hover,
    .vmi-upload-dropzone.vmi-drag-over {
        border-width: 3px;
    }
    
    .vmi-progress-fill {
        background: #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .vmi-upload-dropzone,
    .vmi-progress-fill,
    .vmi-upload-button {
        transition: none;
    }
    
    .vmi-upload-dropzone.vmi-drag-over {
        transform: none;
    }
    
    .vmi-progress-fill::after {
        animation: none;
    }
    
    .vmi-upload-dropzone.vmi-uploading::after {
        animation: none;
    }
}
