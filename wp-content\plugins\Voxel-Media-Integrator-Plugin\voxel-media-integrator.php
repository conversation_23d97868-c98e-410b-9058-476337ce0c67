<?php
/**
 * Plugin Name: Voxel Media Integrator
 * Plugin URI: https://example.com/voxel-media-integrator
 * Description: Enhances the Voxel theme by enabling front-end creation of virtual tours, 3D models, courses, and videos with timeline sharing.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL-2.0-or-later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: voxel-media-integrator
 * Domain Path: /languages
 *
 * Requires at least: 5.0
 * Requires PHP: 7.2
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'VMI_VERSION', '1.0.0' );
define( 'VMI_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'VMI_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'VMI_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

/**
 * Main plugin class
 */
class Voxel_Media_Integrator {

    /**
     * Instance of this class
     *
     * @var Voxel_Media_Integrator
     */
    private static $instance;

    /**
     * Get the singleton instance of this class
     *
     * @return Voxel_Media_Integrator
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Check dependencies
        add_action( 'admin_init', array( $this, 'check_dependencies' ) );

        // Load plugin files
        $this->load_dependencies();

        // Initialize plugin components
        add_action( 'plugins_loaded', array( $this, 'init' ) );

        // Register activation and deactivation hooks
        register_activation_hook( __FILE__, array( $this, 'activate' ) );
        register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
    }

    /**
     * Check if required plugins and theme are active
     */
    public function check_dependencies() {
        // Check if Voxel theme is active
        $theme = wp_get_theme();
        if ( 'Voxel' !== $theme->name && 'Voxel' !== $theme->parent_theme ) {
            add_action( 'admin_notices', array( $this, 'voxel_theme_notice' ) );
            return false;
        }

        // Check if Elementor is active
        if ( ! did_action( 'elementor/loaded' ) ) {
            add_action( 'admin_notices', array( $this, 'elementor_notice' ) );
            return false;
        }

        return true;
    }

    /**
     * Admin notice for missing Voxel theme
     */
    public function voxel_theme_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php esc_html_e( 'Voxel Media Integrator requires the Voxel theme to be installed and activated.', 'voxel-media-integrator' ); ?></p>
        </div>
        <?php
    }

    /**
     * Admin notice for missing Elementor plugin
     */
    public function elementor_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php esc_html_e( 'Voxel Media Integrator requires the Elementor plugin to be installed and activated.', 'voxel-media-integrator' ); ?></p>
        </div>
        <?php
    }

    /**
     * Load required dependencies
     */
    private function load_dependencies() {
        // Core files that must exist
        $core_files = array(
            'includes/class-utils.php',
            'includes/class-post-types.php',
            'includes/class-admin.php',
        );

        // Optional files that we'll load if they exist
        $optional_files = array(
            'includes/class-shortcodes.php',
            'includes/class-template-loader.php',
            'includes/class-file-handler.php',
            'includes/class-virtual-tours.php',
            'includes/class-3d-models.php',
            'includes/class-videos.php',
            'includes/class-templates.php',
            'includes/class-limits.php',
            'includes/class-timeline.php',
            'includes/class-elementor.php',
            'includes/class-frontend-dashboard.php',
            'includes/class-fields.php',
            'includes/class-lms.php',
            'includes/class-frontend-interface.php',
        );

        // Voxel integration files (load separately to avoid conflicts)
        $voxel_files = array(
            'includes/voxel-fields/class-3d-model-field.php',
            'includes/class-voxel-integration.php',
        );

        // Load core files (required)
        foreach ($core_files as $file) {
            $file_path = VMI_PLUGIN_DIR . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            } else {
                // Log error but don't break activation
                error_log("VMI Plugin: Missing core file: $file");
            }
        }

        // Load optional files
        foreach ($optional_files as $file) {
            $file_path = VMI_PLUGIN_DIR . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }

        // Load Voxel integration files only if Voxel is available
        if (function_exists('\Voxel\config') || class_exists('Voxel')) {
            foreach ($voxel_files as $file) {
                $file_path = VMI_PLUGIN_DIR . $file;
                if (file_exists($file_path)) {
                    require_once $file_path;
                }
            }
        }
    }

    /**
     * Initialize plugin components
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain( 'voxel-media-integrator', false, dirname( VMI_PLUGIN_BASENAME ) . '/languages' );

        // Initialize core components (with class existence checks)
        if (class_exists('VMI_Post_Types')) {
            new VMI_Post_Types();
        }
        if (class_exists('VMI_Admin')) {
            new VMI_Admin();
        }

        // Initialize optional components
        if (class_exists('VMI_Shortcodes')) {
            new VMI_Shortcodes();
        }
        if (class_exists('VMI_Template_Loader')) {
            new VMI_Template_Loader();
        }
        if (class_exists('VMI_File_Handler')) {
            new VMI_File_Handler();
        }
        if (class_exists('VMI_Voxel_Integration')) {
            new VMI_Voxel_Integration();
        }
        if (class_exists('VMI_Frontend_Interface')) {
            new VMI_Frontend_Interface();
        }
        if (class_exists('VMI_Virtual_Tours')) {
            new VMI_Virtual_Tours();
        }
        if (class_exists('VMI_3D_Models')) {
            new VMI_3D_Models();
        }
        if (class_exists('VMI_Videos')) {
            new VMI_Videos();
        }
        if (class_exists('VMI_Templates')) {
            new VMI_Templates();
        }
        if (class_exists('VMI_Limits')) {
            new VMI_Limits();
        }
        if (class_exists('VMI_Timeline')) {
            new VMI_Timeline();
        }
        if (class_exists('VMI_Elementor')) {
            new VMI_Elementor();
        }
        if (class_exists('VMI_Frontend_Dashboard')) {
            new VMI_Frontend_Dashboard();
        }
        
        // Initialize LMS
        if (class_exists('VMI_LMS')) {
            global $vmi_lms;
            $vmi_lms = new VMI_LMS();
        }
        
        // Initialize Fields if class exists
        if (class_exists('VMI_Fields')) {
            new VMI_Fields();
        }
        
        // Register scripts and styles
        add_action( 'wp_enqueue_scripts', array( $this, 'register_scripts' ) );
    }

    /**
     * Register scripts and styles
     */
    public function register_scripts() {
        // Register Pannellum.js for virtual tours
        wp_register_script( 'pannellum', 'https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.js', array(), '2.5.6', true );
        wp_register_style( 'pannellum', 'https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.css', array(), '2.5.6' );

        // Register Three.js for 3D models
        wp_register_script( 'three', 'https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js', array(), '0.132.2', true );
        wp_register_script( 'three-gltf-loader', 'https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/GLTFLoader.js', array( 'three' ), '0.132.2', true );
        wp_register_script( 'three-orbit-controls', 'https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js', array( 'three' ), '0.132.2', true );

        // Register model-viewer for 3D models
        wp_register_script( 'model-viewer', 'https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js', array(), null, true );

        // Register plugin scripts
        wp_register_script( 'vmi-virtual-tour-front', VMI_PLUGIN_URL . 'assets/js/virtual-tour-front.js', array( 'pannellum', 'jquery' ), VMI_VERSION, true );
        wp_register_script( 'vmi-3d-model-front', VMI_PLUGIN_URL . 'assets/js/3d-model-front.js', array( 'jquery', 'model-viewer' ), VMI_VERSION, true );
        wp_register_script( 'vmi-video-front', VMI_PLUGIN_URL . 'assets/js/video-front.js', array( 'jquery' ), VMI_VERSION, true );
        wp_register_script( 'vmi-lms-frontend', VMI_PLUGIN_URL . 'assets/js/lms-frontend.js', array( 'jquery' ), VMI_VERSION, true );
        wp_register_script( 'vmi-frontend-main', VMI_PLUGIN_URL . 'assets/js/frontend.js', array( 'jquery' ), VMI_VERSION, true ); // Renamed for clarity
        wp_register_script( 'vmi-timeline', VMI_PLUGIN_URL . 'assets/js/timeline.js', array( 'jquery' ), VMI_VERSION, true );

        // Register plugin styles
        wp_register_style( 'vmi-frontend', VMI_PLUGIN_URL . 'assets/css/frontend.css', array(), VMI_VERSION );

        // Enqueue frontend styles on all pages (for shortcodes)
        wp_enqueue_style( 'vmi-frontend' );

        // Register plugin styles
        wp_register_style( 'vmi-frontend-main', VMI_PLUGIN_URL . 'assets/css/frontend.css', array(), VMI_VERSION ); // Renamed for clarity
        wp_register_style( 'vmi-timeline', VMI_PLUGIN_URL . 'assets/css/timeline.css', array(), VMI_VERSION );
        wp_register_style( 'vmi-lms-frontend', VMI_PLUGIN_URL . 'assets/css/lms-frontend.css', array(), VMI_VERSION );
    }


}

// Activation and deactivation hooks
register_activation_hook( __FILE__, 'vmi_activate_plugin' );
register_deactivation_hook( __FILE__, 'vmi_deactivate_plugin' );

/**
 * Plugin activation function
 */
function vmi_activate_plugin() {
    // Create upload directories
    $upload_dir = wp_upload_dir();
    $vmi_dirs = array(
        $upload_dir['basedir'] . '/vmi-3d-models',
        $upload_dir['basedir'] . '/vmi-videos',
        $upload_dir['basedir'] . '/vmi-images',
    );

    foreach ( $vmi_dirs as $dir ) {
        if ( ! file_exists( $dir ) ) {
            wp_mkdir_p( $dir );
        }
    }

    // Set default options
    $default_limits = array(
        'free' => array('virtual_tours' => 3, '3d_models' => 3, 'videos' => 5, 'lms_courses' => 3),
        'basic' => array('virtual_tours' => 10, '3d_models' => 10, 'videos' => 20, 'lms_courses' => 10),
        'premium' => array('virtual_tours' => 'unlimited', '3d_models' => 'unlimited', 'videos' => 'unlimited', 'lms_courses' => 'unlimited'),
    );

    if ( ! get_option( 'vmi_media_limits' ) ) {
        add_option( 'vmi_media_limits', $default_limits );
    }

    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Plugin deactivation function
 */
function vmi_deactivate_plugin() {
    // Flush rewrite rules
    flush_rewrite_rules();
}

// Debug function for troubleshooting
if ( ! function_exists( 'vmi_debug_log' ) ) {
    function vmi_debug_log( $message ) {
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'VMI Debug: ' . $message );
        }
    }
}

// Initialize the plugin with error handling
add_action( 'plugins_loaded', function() {
    vmi_debug_log( 'Starting plugin initialization' );

    try {
        if ( class_exists( 'Voxel_Media_Integrator' ) ) {
            vmi_debug_log( 'Voxel_Media_Integrator class found, creating instance' );
            Voxel_Media_Integrator::get_instance();
            vmi_debug_log( 'Plugin initialized successfully' );
        } else {
            vmi_debug_log( 'Voxel_Media_Integrator class not found' );
        }
    } catch ( Exception $e ) {
        // Log the error
        error_log( 'VMI Plugin Error: ' . $e->getMessage() );
        vmi_debug_log( 'Plugin initialization failed: ' . $e->getMessage() );

        // Show admin notice
        if ( is_admin() ) {
            add_action( 'admin_notices', function() use ( $e ) {
                echo '<div class="notice notice-error"><p>';
                echo '<strong>Voxel Media Integrator Error:</strong> ' . esc_html( $e->getMessage() );
                echo '</p></div>';
            } );
        }
    } catch ( Error $e ) {
        // Catch fatal errors too
        error_log( 'VMI Plugin Fatal Error: ' . $e->getMessage() );
        vmi_debug_log( 'Plugin fatal error: ' . $e->getMessage() );

        if ( is_admin() ) {
            add_action( 'admin_notices', function() use ( $e ) {
                echo '<div class="notice notice-error"><p>';
                echo '<strong>Voxel Media Integrator Fatal Error:</strong> ' . esc_html( $e->getMessage() );
                echo '</p></div>';
            } );
        }
    }
} );
?>
