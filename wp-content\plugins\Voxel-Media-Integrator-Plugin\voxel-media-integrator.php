<?php
/**
 * Plugin Name: Voxel Media Integrator
 * Plugin URI: https://example.com/voxel-media-integrator
 * Description: Enhances the Voxel theme by enabling front-end creation of virtual tours, 3D models, courses, and videos with timeline sharing.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL-2.0-or-later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: voxel-media-integrator
 * Domain Path: /languages
 *
 * Requires at least: 5.0
 * Requires PHP: 7.2
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'VMI_VERSION', '1.0.0' );
define( 'VMI_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'VMI_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'VMI_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

/**
 * Main plugin class
 */
class Voxel_Media_Integrator {

    /**
     * Instance of this class
     *
     * @var Voxel_Media_Integrator
     */
    private static $instance;

    /**
     * Get the singleton instance of this class
     *
     * @return Voxel_Media_Integrator
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Check dependencies
        add_action( 'admin_init', array( $this, 'check_dependencies' ) );

        // Load plugin files
        $this->load_dependencies();

        // Initialize plugin components
        add_action( 'plugins_loaded', array( $this, 'init' ) );

        // Register activation and deactivation hooks
        register_activation_hook( __FILE__, array( $this, 'activate' ) );
        register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
    }

    /**
     * Check if required plugins and theme are active
     */
    public function check_dependencies() {
        // Check if Voxel theme is active
        $theme = wp_get_theme();
        if ( 'Voxel' !== $theme->name && 'Voxel' !== $theme->parent_theme ) {
            add_action( 'admin_notices', array( $this, 'voxel_theme_notice' ) );
            return false;
        }

        // Check if Elementor is active
        if ( ! did_action( 'elementor/loaded' ) ) {
            add_action( 'admin_notices', array( $this, 'elementor_notice' ) );
            return false;
        }

        return true;
    }

    /**
     * Admin notice for missing Voxel theme
     */
    public function voxel_theme_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php esc_html_e( 'Voxel Media Integrator requires the Voxel theme to be installed and activated.', 'voxel-media-integrator' ); ?></p>
        </div>
        <?php
    }

    /**
     * Admin notice for missing Elementor plugin
     */
    public function elementor_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php esc_html_e( 'Voxel Media Integrator requires the Elementor plugin to be installed and activated.', 'voxel-media-integrator' ); ?></p>
        </div>
        <?php
    }

    /**
     * Load required dependencies
     */
    private function load_dependencies() {
        // Include core files
        require_once VMI_PLUGIN_DIR . 'includes/class-utils.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-post-types.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-shortcodes.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-template-loader.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-file-handler.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-voxel-integration.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-frontend-interface.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-virtual-tours.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-3d-models.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-videos.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-templates.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-admin.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-limits.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-timeline.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-elementor.php';
        require_once VMI_PLUGIN_DIR . 'includes/class-frontend-dashboard.php'; 
        
        // Include additional files if they exist
        if (file_exists(VMI_PLUGIN_DIR . 'includes/class-fields.php')) {
            require_once VMI_PLUGIN_DIR . 'includes/class-fields.php';
        }
        if (file_exists(VMI_PLUGIN_DIR . 'includes/class-lms.php')) {
            require_once VMI_PLUGIN_DIR . 'includes/class-lms.php';
        }
    }

    /**
     * Initialize plugin components
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain( 'voxel-media-integrator', false, dirname( VMI_PLUGIN_BASENAME ) . '/languages' );

        // Initialize core components
        new VMI_Post_Types();
        new VMI_Shortcodes(); // Add shortcodes support
        new VMI_Template_Loader(); // Add template loading support
        new VMI_File_Handler(); // Add file upload support
        new VMI_Voxel_Integration(); // Add Voxel theme integration
        new VMI_Frontend_Interface(); // Add frontend user interface
        new VMI_Virtual_Tours();
        new VMI_3D_Models();
        new VMI_Videos();
        new VMI_Templates();
        new VMI_Admin();
        new VMI_Limits();
        new VMI_Timeline();
        new VMI_Elementor();
        if (class_exists('VMI_Frontend_Dashboard')) { 
            new VMI_Frontend_Dashboard();
        }
        
        // Initialize LMS
        if (class_exists('VMI_LMS')) {
            global $vmi_lms;
            $vmi_lms = new VMI_LMS();
        }
        
        // Initialize Fields if class exists
        if (class_exists('VMI_Fields')) {
            new VMI_Fields();
        }
        
        // Register scripts and styles
        add_action( 'wp_enqueue_scripts', array( $this, 'register_scripts' ) );
    }

    /**
     * Register scripts and styles
     */
    public function register_scripts() {
        // Register Pannellum.js for virtual tours
        wp_register_script( 'pannellum', 'https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.js', array(), '2.5.6', true );
        wp_register_style( 'pannellum', 'https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.css', array(), '2.5.6' );

        // Register Three.js for 3D models
        wp_register_script( 'three', 'https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js', array(), '0.132.2', true );
        wp_register_script( 'three-gltf-loader', 'https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/GLTFLoader.js', array( 'three' ), '0.132.2', true );
        wp_register_script( 'three-orbit-controls', 'https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js', array( 'three' ), '0.132.2', true );

        // Register model-viewer for 3D models
        wp_register_script( 'model-viewer', 'https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js', array(), null, true );

        // Register plugin scripts
        wp_register_script( 'vmi-virtual-tour-front', VMI_PLUGIN_URL . 'assets/js/virtual-tour-front.js', array( 'pannellum', 'jquery' ), VMI_VERSION, true );
        wp_register_script( 'vmi-3d-model-front', VMI_PLUGIN_URL . 'assets/js/3d-model-front.js', array( 'jquery', 'model-viewer' ), VMI_VERSION, true );
        wp_register_script( 'vmi-video-front', VMI_PLUGIN_URL . 'assets/js/video-front.js', array( 'jquery' ), VMI_VERSION, true );
        wp_register_script( 'vmi-lms-frontend', VMI_PLUGIN_URL . 'assets/js/lms-frontend.js', array( 'jquery' ), VMI_VERSION, true );
        wp_register_script( 'vmi-frontend-main', VMI_PLUGIN_URL . 'assets/js/frontend.js', array( 'jquery' ), VMI_VERSION, true ); // Renamed for clarity
        wp_register_script( 'vmi-timeline', VMI_PLUGIN_URL . 'assets/js/timeline.js', array( 'jquery' ), VMI_VERSION, true );

        // Register plugin styles
        wp_register_style( 'vmi-frontend', VMI_PLUGIN_URL . 'assets/css/frontend.css', array(), VMI_VERSION );

        // Enqueue frontend styles on all pages (for shortcodes)
        wp_enqueue_style( 'vmi-frontend' );

        // Register plugin styles
        wp_register_style( 'vmi-frontend-main', VMI_PLUGIN_URL . 'assets/css/frontend.css', array(), VMI_VERSION ); // Renamed for clarity
        wp_register_style( 'vmi-timeline', VMI_PLUGIN_URL . 'assets/css/timeline.css', array(), VMI_VERSION );
        wp_register_style( 'vmi-lms-frontend', VMI_PLUGIN_URL . 'assets/css/lms-frontend.css', array(), VMI_VERSION );
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create default options
        $default_limits = array(
            'free' => ['virtual_tours' => 3, '3d_models' => 3, 'videos' => 5, 'lms_courses' => 3],
            'basic' => ['virtual_tours' => 10, '3d_models' => 10, 'videos' => 20, 'lms_courses' => 10],
            'premium' => ['virtual_tours' => 'unlimited', '3d_models' => 'unlimited', 'videos' => 'unlimited', 'lms_courses' => 'unlimited'],
        );

        if ( ! get_option( 'vmi_media_limits' ) ) {
            add_option( 'vmi_media_limits', $default_limits );
        }

        // Register post types
        if (class_exists('VMI_Post_Types')) {
            $post_types = new VMI_Post_Types();
            $post_types->register_post_types();
            $post_types->register_taxonomies();
        }
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

// Initialize the plugin
Voxel_Media_Integrator::get_instance();
?>
