/**
 * Frontend styles for Voxel Media Integrator
 */

/* Hero media */
.vmi-hero-media {
    margin-bottom: 30px;
    width: 100%;
}

.vmi-hero-media video {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: contain;
}

.vmi-hero-media iframe {
    width: 100%;
    height: 500px;
    border: none;
}

.vmi-hero-media model-viewer {
    width: 100%;
    height: 500px;
}

/* Media sections */
.vmi-section {
    margin-bottom: 40px;
}

.vmi-section h2 {
    margin-bottom: 20px;
}

.vmi-virtual-tour,
.vmi-course,
.vmi-video,
.vmi-3d-model {
    margin-bottom: 30px;
}

.vmi-video video {
    width: 100%;
    height: auto;
    max-height: 400px;
}

.vmi-video iframe {
    width: 100%;
    height: 400px;
    border: none;
}

.vmi-3d-model model-viewer {
    width: 100%;
    height: 400px;
}

/* Media loop */
.vmi-media-loop {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.vmi-media-loop-item {
    border: 1px solid #eee;
    border-radius: 5px;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.vmi-media-loop-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.vmi-media-loop-item h3 {
    padding: 15px;
    margin: 0;
    background-color: #f9f9f9;
}

.vmi-media-loop-item .vmi-hero-media {
    height: 200px;
    margin-bottom: 0;
}

.vmi-media-loop-item .vmi-hero-media video,
.vmi-media-loop-item .vmi-hero-media iframe,
.vmi-media-loop-item .vmi-hero-media model-viewer {
    height: 200px;
}

.vmi-media-summary {
    padding: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.vmi-media-count {
    background-color: #f0f0f0;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 0.9em;
}

/* Shortcode Container Styles */
.vmi-3d-model-container,
.vmi-video-container,
.vmi-virtual-tour-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
}

.vmi-3d-model-container model-viewer {
    background-color: #f8f9fa;
}

.vmi-video-container iframe,
.vmi-video-container video {
    border-radius: 8px;
}

/* Course Container Styles */
.vmi-course-container {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.vmi-course-container h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.vmi-course-content {
    margin: 15px 0;
    line-height: 1.6;
}

.vmi-course-lessons {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.vmi-course-lessons h4 {
    color: #34495e;
    margin-bottom: 15px;
}

.vmi-course-lessons ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.vmi-course-lessons li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f1f1;
}

.vmi-course-lessons li:last-child {
    border-bottom: none;
}

.vmi-course-lessons a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.vmi-course-lessons a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Media Gallery Styles */
.vmi-media-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px 0;
}

.vmi-media-item {
    box-sizing: border-box;
    padding: 0 10px;
    margin-bottom: 20px;
}

.vmi-media-item-inner {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.vmi-media-item-inner:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.vmi-media-thumbnail {
    position: relative;
    overflow: hidden;
}

.vmi-media-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.vmi-media-thumbnail:hover img {
    transform: scale(1.05);
}

.vmi-media-content {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.vmi-media-content h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    line-height: 1.4;
}

.vmi-media-content h4 a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.vmi-media-content h4 a:hover {
    color: #3498db;
}

.vmi-media-type {
    color: #7f8c8d;
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 600;
    margin: 0 0 10px 0;
    letter-spacing: 0.5px;
}

.vmi-media-excerpt {
    color: #5a6c7d;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
    flex-grow: 1;
}

/* Column Layouts */
.vmi-columns-1 .vmi-media-item { width: 100%; }
.vmi-columns-2 .vmi-media-item { width: 50%; }
.vmi-columns-3 .vmi-media-item { width: 33.333%; }
.vmi-columns-4 .vmi-media-item { width: 25%; }
.vmi-columns-5 .vmi-media-item { width: 20%; }
.vmi-columns-6 .vmi-media-item { width: 16.666%; }

/* Responsive styles */
@media screen and (max-width: 768px) {
    .vmi-hero-media iframe,
    .vmi-hero-media model-viewer {
        height: 300px;
    }

    .vmi-video iframe,
    .vmi-3d-model model-viewer {
        height: 250px;
    }

    .vmi-media-loop {
        grid-template-columns: 1fr;
    }

    .vmi-columns-3 .vmi-media-item,
    .vmi-columns-4 .vmi-media-item,
    .vmi-columns-5 .vmi-media-item,
    .vmi-columns-6 .vmi-media-item {
        width: 50%;
    }

    .vmi-media-gallery {
        gap: 15px;
    }

    .vmi-media-item {
        padding: 0 7.5px;
    }
}

@media screen and (max-width: 480px) {
    .vmi-columns-2 .vmi-media-item,
    .vmi-columns-3 .vmi-media-item,
    .vmi-columns-4 .vmi-media-item,
    .vmi-columns-5 .vmi-media-item,
    .vmi-columns-6 .vmi-media-item {
        width: 100%;
    }

    .vmi-media-gallery {
        gap: 10px;
    }

    .vmi-media-item {
        padding: 0 5px;
    }

    .vmi-3d-model-container,
    .vmi-video-container,
    .vmi-virtual-tour-container {
        height: 250px !important;
    }
}
