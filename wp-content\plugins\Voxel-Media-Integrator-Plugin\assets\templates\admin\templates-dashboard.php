<?php
/**
 * Template for displaying templates dashboard with visual widget
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-main">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <!-- Template Categories Filter -->
    <div class="vmi-template-filters">
        <button class="vmi-filter-btn active" data-category="all">
            <span class="dashicons dashicons-admin-media"></span>
            <?php esc_html_e('All Templates', 'voxel-media-integrator'); ?>
        </button>
        <?php foreach ($template_categories as $category_key => $category): ?>
            <button class="vmi-filter-btn" data-category="<?php echo esc_attr($category_key); ?>" style="--category-color: <?php echo esc_attr($category['color']); ?>">
                <span class="dashicons dashicons-<?php echo esc_attr($category['icon']); ?>"></span>
                <?php echo esc_html($category['title']); ?>
            </button>
        <?php endforeach; ?>
    </div>

    <!-- Templates Grid -->
    <div class="vmi-templates-grid">
        <?php foreach ($templates_metadata as $template_key => $template): ?>
            <div class="vmi-template-card" data-category="<?php echo esc_attr($template['category']); ?>">
                <div class="vmi-template-preview">
                    <div class="vmi-template-image">
                        <?php
                        $preview_url = plugin_dir_url(dirname(dirname(__FILE__))) . 'images/template-previews/' . $template['preview_image'];
                        $placeholder_color = $template_categories[$template['category']]['color'];
                        ?>
                        <div class="vmi-template-placeholder" style="background: linear-gradient(135deg, <?php echo esc_attr($placeholder_color); ?>20, <?php echo esc_attr($placeholder_color); ?>40);">
                            <span class="dashicons dashicons-<?php echo esc_attr($template_categories[$template['category']]['icon']); ?>" style="color: <?php echo esc_attr($placeholder_color); ?>;"></span>
                        </div>
                        <div class="vmi-template-overlay">
                            <button class="vmi-preview-btn" data-template="<?php echo esc_attr($template_key); ?>">
                                <span class="dashicons dashicons-visibility"></span>
                                <?php esc_html_e('Preview', 'voxel-media-integrator'); ?>
                            </button>
                        </div>
                    </div>

                    <div class="vmi-template-category-badge" style="background: <?php echo esc_attr($template_categories[$template['category']]['color']); ?>;">
                        <span class="dashicons dashicons-<?php echo esc_attr($template_categories[$template['category']]['icon']); ?>"></span>
                        <?php echo esc_html($template_categories[$template['category']]['title']); ?>
                    </div>
                </div>

                <div class="vmi-template-content">
                    <h3 class="vmi-template-title"><?php echo esc_html($template['title']); ?></h3>
                    <p class="vmi-template-description"><?php echo esc_html($template['description']); ?></p>

                    <div class="vmi-template-features">
                        <?php foreach ($template['features'] as $feature): ?>
                            <span class="vmi-feature-tag"><?php echo esc_html($feature); ?></span>
                        <?php endforeach; ?>
                    </div>

                    <div class="vmi-template-actions">
                        <div class="vmi-shortcode-container">
                            <input type="text" class="vmi-shortcode-input" value="<?php echo esc_attr($template['shortcode']); ?>" readonly>
                            <button class="vmi-copy-btn" data-shortcode="<?php echo esc_attr($template['shortcode']); ?>">
                                <span class="dashicons dashicons-admin-page"></span>
                                <?php esc_html_e('Copy', 'voxel-media-integrator'); ?>
                            </button>
                        </div>

                        <div class="vmi-template-buttons">
                            <button class="button button-primary vmi-use-template" data-template="<?php echo esc_attr($template_key); ?>">
                                <span class="dashicons dashicons-plus-alt2"></span>
                                <?php esc_html_e('Use Template', 'voxel-media-integrator'); ?>
                            </button>
                            <button class="button vmi-customize-template" data-template="<?php echo esc_attr($template_key); ?>">
                                <span class="dashicons dashicons-admin-customizer"></span>
                                <?php esc_html_e('Customize', 'voxel-media-integrator'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>


</div>

<script>
jQuery(document).ready(function($) {
    // Template filtering
    $('.vmi-filter-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const category = $(this).data('category');

        $('.vmi-filter-btn').removeClass('active');
        $(this).addClass('active');

        if (category === 'all') {
            $('.vmi-template-card').fadeIn(300);
        } else {
            $('.vmi-template-card').fadeOut(200);
            setTimeout(function() {
                $(`.vmi-template-card[data-category="${category}"]`).fadeIn(300);
            }, 200);
        }
    });

    // Copy shortcode functionality
    $('.vmi-copy-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const shortcode = $(this).data('shortcode');
        const input = $(this).siblings('.vmi-shortcode-input');

        // Select and copy text
        input[0].select();
        input[0].setSelectionRange(0, 99999); // For mobile devices

        try {
            document.execCommand('copy');

            // Visual feedback
            const originalText = $(this).text();
            $(this).text('Copied!').addClass('copied');

            setTimeout(() => {
                $(this).text(originalText).removeClass('copied');
            }, 2000);
        } catch (err) {
            console.log('Copy failed:', err);
        }
    });

    // Use template button
    $('.vmi-use-template').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const template = $(this).data('template');
        const shortcode = $(this).closest('.vmi-template-card').find('.vmi-shortcode-input').val();

        // Show success message
        alert('Template shortcode: ' + shortcode + '\n\nYou can now paste this shortcode into any post or page.');
    });

    // Customize template button
    $('.vmi-customize-template').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const template = $(this).data('template');
        alert('Customization panel coming soon! For now, you can modify the shortcode parameters manually.');
    });

    // Preview button
    $('.vmi-preview-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const template = $(this).data('template');
        alert('Preview functionality coming soon!');
    });

    // Prevent form submission issues
    $('form').on('submit', function(e) {
        // Allow normal form submission
        return true;
    });
});
</script>
