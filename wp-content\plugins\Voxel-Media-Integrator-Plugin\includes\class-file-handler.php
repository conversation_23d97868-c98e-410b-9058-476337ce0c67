<?php
/**
 * File Upload and Management System
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class for handling file uploads and management
 */
class VMI_File_Handler {

    /**
     * Allowed 3D model file types
     */
    const ALLOWED_3D_FORMATS = array(
        'glb' => 'model/gltf-binary',
        'gltf' => 'model/gltf+json',
        'obj' => 'text/plain',
        'fbx' => 'application/octet-stream',
        'dae' => 'model/vnd.collada+xml',
        'stl' => 'application/sla',
        'ply' => 'application/octet-stream',
    );

    /**
     * Allowed video file types
     */
    const ALLOWED_VIDEO_FORMATS = array(
        'mp4' => 'video/mp4',
        'webm' => 'video/webm',
        'ogg' => 'video/ogg',
        'avi' => 'video/x-msvideo',
        'mov' => 'video/quicktime',
        'wmv' => 'video/x-ms-wmv',
    );

    /**
     * Allowed image file types for thumbnails
     */
    const ALLOWED_IMAGE_FORMATS = array(
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
    );

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'wp_ajax_vmi_upload_3d_model', array( $this, 'handle_3d_model_upload' ) );
        add_action( 'wp_ajax_vmi_upload_video', array( $this, 'handle_video_upload' ) );
        add_action( 'wp_ajax_vmi_upload_thumbnail', array( $this, 'handle_thumbnail_upload' ) );
        add_action( 'wp_ajax_vmi_delete_media_file', array( $this, 'handle_file_deletion' ) );
        
        // Add upload fields to admin
        add_action( 'add_meta_boxes', array( $this, 'add_upload_meta_boxes' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_upload_scripts' ) );
        
        // Modify allowed file types
        add_filter( 'upload_mimes', array( $this, 'add_custom_mime_types' ) );
        add_filter( 'wp_check_filetype_and_ext', array( $this, 'check_custom_file_types' ), 10, 4 );
    }

    /**
     * Add custom MIME types for uploads
     *
     * @param array $mimes Existing MIME types.
     * @return array Modified MIME types.
     */
    public function add_custom_mime_types( $mimes ) {
        // Add 3D model formats
        $mimes['glb'] = 'model/gltf-binary';
        $mimes['gltf'] = 'model/gltf+json';
        $mimes['obj'] = 'text/plain';
        $mimes['fbx'] = 'application/octet-stream';
        $mimes['dae'] = 'model/vnd.collada+xml';
        $mimes['stl'] = 'application/sla';
        $mimes['ply'] = 'application/octet-stream';

        return $mimes;
    }

    /**
     * Check custom file types
     *
     * @param array  $wp_check_filetype_and_ext File data.
     * @param string $file File path.
     * @param string $filename File name.
     * @param array  $mimes MIME types.
     * @return array Modified file data.
     */
    public function check_custom_file_types( $wp_check_filetype_and_ext, $file, $filename, $mimes ) {
        if ( ! $wp_check_filetype_and_ext['type'] ) {
            $check_filetype = wp_check_filetype( $filename, $mimes );
            $ext = $check_filetype['ext'];
            $type = $check_filetype['type'];

            // Check for 3D model files
            if ( in_array( $ext, array_keys( self::ALLOWED_3D_FORMATS ) ) ) {
                $wp_check_filetype_and_ext['ext'] = $ext;
                $wp_check_filetype_and_ext['type'] = $type;
            }
        }

        return $wp_check_filetype_and_ext;
    }

    /**
     * Enqueue upload scripts
     *
     * @param string $hook Current admin page.
     */
    public function enqueue_upload_scripts( $hook ) {
        global $post_type;

        $vmi_post_types = array( 'vmi_3d_models', 'vmi_videos', 'vmi_virtual_tours' );
        
        if ( in_array( $post_type, $vmi_post_types ) && ( $hook === 'post.php' || $hook === 'post-new.php' ) ) {
            wp_enqueue_media();
            wp_enqueue_script( 'vmi-file-upload', VMI_PLUGIN_URL . 'assets/js/file-upload.js', array( 'jquery', 'media-upload' ), VMI_VERSION, true );
            wp_enqueue_style( 'vmi-file-upload', VMI_PLUGIN_URL . 'assets/css/file-upload.css', array(), VMI_VERSION );
            
            wp_localize_script( 'vmi-file-upload', 'vmi_upload', array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'vmi_upload_nonce' ),
                'max_file_size' => wp_max_upload_size(),
                'allowed_3d_formats' => array_keys( self::ALLOWED_3D_FORMATS ),
                'allowed_video_formats' => array_keys( self::ALLOWED_VIDEO_FORMATS ),
                'allowed_image_formats' => array_keys( self::ALLOWED_IMAGE_FORMATS ),
                'strings' => array(
                    'upload_error' => __( 'Upload failed. Please try again.', 'voxel-media-integrator' ),
                    'file_too_large' => __( 'File is too large.', 'voxel-media-integrator' ),
                    'invalid_format' => __( 'Invalid file format.', 'voxel-media-integrator' ),
                    'upload_success' => __( 'File uploaded successfully!', 'voxel-media-integrator' ),
                    'delete_confirm' => __( 'Are you sure you want to delete this file?', 'voxel-media-integrator' ),
                )
            ) );
        }
    }

    /**
     * Add upload meta boxes
     */
    public function add_upload_meta_boxes() {
        add_meta_box(
            'vmi_file_upload',
            __( 'File Upload', 'voxel-media-integrator' ),
            array( $this, 'render_upload_meta_box' ),
            array( 'vmi_3d_models', 'vmi_videos' ),
            'side',
            'high'
        );
    }

    /**
     * Render upload meta box
     *
     * @param WP_Post $post Current post object.
     */
    public function render_upload_meta_box( $post ) {
        $post_type = $post->post_type;
        
        echo '<div class="vmi-upload-container">';
        
        if ( $post_type === 'vmi_3d_models' ) {
            $this->render_3d_model_upload( $post );
        } elseif ( $post_type === 'vmi_videos' ) {
            $this->render_video_upload( $post );
        }
        
        echo '</div>';
    }

    /**
     * Render 3D model upload interface
     *
     * @param WP_Post $post Current post object.
     */
    private function render_3d_model_upload( $post ) {
        $model_file = get_post_meta( $post->ID, 'vmi_model_file', true );
        $model_format = get_post_meta( $post->ID, 'vmi_model_format', true );
        
        echo '<div class="vmi-upload-section">';
        echo '<h4>' . __( '3D Model File', 'voxel-media-integrator' ) . '</h4>';
        
        if ( $model_file ) {
            echo '<div class="vmi-current-file">';
            echo '<p><strong>' . __( 'Current file:', 'voxel-media-integrator' ) . '</strong></p>';
            echo '<p><a href="' . esc_url( $model_file ) . '" target="_blank">' . basename( $model_file ) . '</a></p>';
            echo '<p><em>' . esc_html( strtoupper( $model_format ) ) . ' format</em></p>';
            echo '<button type="button" class="button vmi-delete-file" data-file-type="model" data-post-id="' . $post->ID . '">';
            echo __( 'Remove File', 'voxel-media-integrator' ) . '</button>';
            echo '</div>';
        }
        
        echo '<div class="vmi-upload-area" data-upload-type="3d_model" data-post-id="' . $post->ID . '">';
        echo '<div class="vmi-upload-dropzone">';
        echo '<div class="vmi-upload-icon">📦</div>';
        echo '<p>' . __( 'Drop 3D model file here or click to upload', 'voxel-media-integrator' ) . '</p>';
        echo '<p class="vmi-upload-formats"><small>' . __( 'Supported formats: GLB, GLTF, OBJ, FBX, DAE, STL', 'voxel-media-integrator' ) . '</small></p>';
        echo '<input type="file" class="vmi-file-input" accept=".glb,.gltf,.obj,.fbx,.dae,.stl" style="display: none;">';
        echo '<button type="button" class="button vmi-upload-button">' . __( 'Choose File', 'voxel-media-integrator' ) . '</button>';
        echo '</div>';
        echo '<div class="vmi-upload-progress" style="display: none;">';
        echo '<div class="vmi-progress-bar"><div class="vmi-progress-fill"></div></div>';
        echo '<p class="vmi-progress-text">0%</p>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * Render video upload interface
     *
     * @param WP_Post $post Current post object.
     */
    private function render_video_upload( $post ) {
        $video_url = get_post_meta( $post->ID, 'vmi_video_url', true );
        $video_type = get_post_meta( $post->ID, 'vmi_video_type', true );
        
        echo '<div class="vmi-upload-section">';
        echo '<h4>' . __( 'Video File', 'voxel-media-integrator' ) . '</h4>';
        
        if ( $video_url && $video_type === 'self_hosted' ) {
            echo '<div class="vmi-current-file">';
            echo '<p><strong>' . __( 'Current file:', 'voxel-media-integrator' ) . '</strong></p>';
            echo '<p><a href="' . esc_url( $video_url ) . '" target="_blank">' . basename( $video_url ) . '</a></p>';
            echo '<button type="button" class="button vmi-delete-file" data-file-type="video" data-post-id="' . $post->ID . '">';
            echo __( 'Remove File', 'voxel-media-integrator' ) . '</button>';
            echo '</div>';
        }
        
        echo '<div class="vmi-upload-area" data-upload-type="video" data-post-id="' . $post->ID . '">';
        echo '<div class="vmi-upload-dropzone">';
        echo '<div class="vmi-upload-icon">🎬</div>';
        echo '<p>' . __( 'Drop video file here or click to upload', 'voxel-media-integrator' ) . '</p>';
        echo '<p class="vmi-upload-formats"><small>' . __( 'Supported formats: MP4, WebM, OGG, AVI, MOV', 'voxel-media-integrator' ) . '</small></p>';
        echo '<input type="file" class="vmi-file-input" accept=".mp4,.webm,.ogg,.avi,.mov" style="display: none;">';
        echo '<button type="button" class="button vmi-upload-button">' . __( 'Choose File', 'voxel-media-integrator' ) . '</button>';
        echo '</div>';
        echo '<div class="vmi-upload-progress" style="display: none;">';
        echo '<div class="vmi-progress-bar"><div class="vmi-progress-fill"></div></div>';
        echo '<p class="vmi-progress-text">0%</p>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="vmi-upload-note">';
        echo '<p><small>' . __( 'Note: For YouTube or Vimeo videos, use the Video URL field in the main editor instead.', 'voxel-media-integrator' ) . '</small></p>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * Handle 3D model upload
     */
    public function handle_3d_model_upload() {
        check_ajax_referer( 'vmi_upload_nonce', 'nonce' );

        if ( ! current_user_can( 'upload_files' ) ) {
            wp_die( __( 'You do not have permission to upload files.', 'voxel-media-integrator' ) );
        }

        $post_id = intval( $_POST['post_id'] );
        
        if ( ! $post_id || get_post_type( $post_id ) !== 'vmi_3d_models' ) {
            wp_send_json_error( __( 'Invalid post ID.', 'voxel-media-integrator' ) );
        }

        if ( empty( $_FILES['file'] ) ) {
            wp_send_json_error( __( 'No file uploaded.', 'voxel-media-integrator' ) );
        }

        $file = $_FILES['file'];
        $upload_result = $this->process_file_upload( $file, 'model' );

        if ( is_wp_error( $upload_result ) ) {
            wp_send_json_error( $upload_result->get_error_message() );
        }

        // Update post meta
        update_post_meta( $post_id, 'vmi_model_file', $upload_result['url'] );
        update_post_meta( $post_id, 'vmi_model_format', $upload_result['ext'] );
        update_post_meta( $post_id, 'vmi_model_size', $upload_result['size'] );

        wp_send_json_success( array(
            'url' => $upload_result['url'],
            'filename' => $upload_result['filename'],
            'format' => strtoupper( $upload_result['ext'] ),
            'size' => size_format( $upload_result['size'] ),
            'message' => __( '3D model uploaded successfully!', 'voxel-media-integrator' )
        ) );
    }

    /**
     * Handle video upload
     */
    public function handle_video_upload() {
        check_ajax_referer( 'vmi_upload_nonce', 'nonce' );

        if ( ! current_user_can( 'upload_files' ) ) {
            wp_die( __( 'You do not have permission to upload files.', 'voxel-media-integrator' ) );
        }

        $post_id = intval( $_POST['post_id'] );
        
        if ( ! $post_id || get_post_type( $post_id ) !== 'vmi_videos' ) {
            wp_send_json_error( __( 'Invalid post ID.', 'voxel-media-integrator' ) );
        }

        if ( empty( $_FILES['file'] ) ) {
            wp_send_json_error( __( 'No file uploaded.', 'voxel-media-integrator' ) );
        }

        $file = $_FILES['file'];
        $upload_result = $this->process_file_upload( $file, 'video' );

        if ( is_wp_error( $upload_result ) ) {
            wp_send_json_error( $upload_result->get_error_message() );
        }

        // Update post meta
        update_post_meta( $post_id, 'vmi_video_url', $upload_result['url'] );
        update_post_meta( $post_id, 'vmi_video_type', 'self_hosted' );
        update_post_meta( $post_id, 'vmi_video_format', $upload_result['ext'] );
        update_post_meta( $post_id, 'vmi_video_size', $upload_result['size'] );

        wp_send_json_success( array(
            'url' => $upload_result['url'],
            'filename' => $upload_result['filename'],
            'format' => strtoupper( $upload_result['ext'] ),
            'size' => size_format( $upload_result['size'] ),
            'message' => __( 'Video uploaded successfully!', 'voxel-media-integrator' )
        ) );
    }

    /**
     * Handle file deletion
     */
    public function handle_file_deletion() {
        check_ajax_referer( 'vmi_upload_nonce', 'nonce' );

        if ( ! current_user_can( 'delete_posts' ) ) {
            wp_die( __( 'You do not have permission to delete files.', 'voxel-media-integrator' ) );
        }

        $post_id = intval( $_POST['post_id'] );
        $file_type = sanitize_text_field( $_POST['file_type'] );

        if ( ! $post_id ) {
            wp_send_json_error( __( 'Invalid post ID.', 'voxel-media-integrator' ) );
        }

        if ( $file_type === 'model' ) {
            delete_post_meta( $post_id, 'vmi_model_file' );
            delete_post_meta( $post_id, 'vmi_model_format' );
            delete_post_meta( $post_id, 'vmi_model_size' );
        } elseif ( $file_type === 'video' ) {
            delete_post_meta( $post_id, 'vmi_video_url' );
            delete_post_meta( $post_id, 'vmi_video_format' );
            delete_post_meta( $post_id, 'vmi_video_size' );
        }

        wp_send_json_success( array(
            'message' => __( 'File removed successfully!', 'voxel-media-integrator' )
        ) );
    }

    /**
     * Process file upload
     *
     * @param array  $file File data from $_FILES.
     * @param string $type File type (model, video, image).
     * @return array|WP_Error Upload result or error.
     */
    private function process_file_upload( $file, $type ) {
        // Validate file
        $validation = $this->validate_file( $file, $type );
        if ( is_wp_error( $validation ) ) {
            return $validation;
        }

        // Set up upload directory
        $upload_dir = $this->get_upload_directory( $type );
        if ( is_wp_error( $upload_dir ) ) {
            return $upload_dir;
        }

        // Generate unique filename
        $filename = $this->generate_unique_filename( $file['name'], $upload_dir['path'] );
        $file_path = $upload_dir['path'] . '/' . $filename;
        $file_url = $upload_dir['url'] . '/' . $filename;

        // Move uploaded file
        if ( ! move_uploaded_file( $file['tmp_name'], $file_path ) ) {
            return new WP_Error( 'upload_failed', __( 'Failed to move uploaded file.', 'voxel-media-integrator' ) );
        }

        // Set proper file permissions
        chmod( $file_path, 0644 );

        $file_info = pathinfo( $filename );

        return array(
            'path' => $file_path,
            'url' => $file_url,
            'filename' => $filename,
            'ext' => $file_info['extension'],
            'size' => filesize( $file_path ),
        );
    }

    /**
     * Validate uploaded file
     *
     * @param array  $file File data from $_FILES.
     * @param string $type File type (model, video, image).
     * @return bool|WP_Error True if valid, WP_Error if invalid.
     */
    private function validate_file( $file, $type ) {
        // Check for upload errors
        if ( $file['error'] !== UPLOAD_ERR_OK ) {
            return new WP_Error( 'upload_error', $this->get_upload_error_message( $file['error'] ) );
        }

        // Check file size
        $max_size = $this->get_max_file_size( $type );
        if ( $file['size'] > $max_size ) {
            return new WP_Error( 'file_too_large', sprintf(
                __( 'File is too large. Maximum size is %s.', 'voxel-media-integrator' ),
                size_format( $max_size )
            ) );
        }

        // Check file extension
        $file_info = pathinfo( $file['name'] );
        $extension = strtolower( $file_info['extension'] );

        $allowed_formats = $this->get_allowed_formats( $type );
        if ( ! array_key_exists( $extension, $allowed_formats ) ) {
            return new WP_Error( 'invalid_format', sprintf(
                __( 'Invalid file format. Allowed formats: %s', 'voxel-media-integrator' ),
                implode( ', ', array_keys( $allowed_formats ) )
            ) );
        }

        // Check MIME type
        $finfo = finfo_open( FILEINFO_MIME_TYPE );
        $mime_type = finfo_file( $finfo, $file['tmp_name'] );
        finfo_close( $finfo );

        // Some formats may have multiple valid MIME types, so we'll be flexible
        if ( $type === 'model' && ! in_array( $mime_type, array_values( $allowed_formats ) ) ) {
            // For 3D models, we'll be more lenient as MIME detection can be unreliable
            if ( ! in_array( $extension, array( 'glb', 'gltf', 'obj', 'fbx', 'dae', 'stl', 'ply' ) ) ) {
                return new WP_Error( 'invalid_mime', __( 'Invalid file type detected.', 'voxel-media-integrator' ) );
            }
        }

        return true;
    }

    /**
     * Get upload directory for file type
     *
     * @param string $type File type.
     * @return array|WP_Error Upload directory info or error.
     */
    private function get_upload_directory( $type ) {
        $wp_upload_dir = wp_upload_dir();

        if ( $wp_upload_dir['error'] ) {
            return new WP_Error( 'upload_dir_error', $wp_upload_dir['error'] );
        }

        $subdir = '';
        switch ( $type ) {
            case 'model':
                $subdir = '/vmi-3d-models';
                break;
            case 'video':
                $subdir = '/vmi-videos';
                break;
            case 'image':
                $subdir = '/vmi-images';
                break;
            default:
                $subdir = '/vmi-media';
                break;
        }

        $upload_path = $wp_upload_dir['basedir'] . $subdir;
        $upload_url = $wp_upload_dir['baseurl'] . $subdir;

        // Create directory if it doesn't exist
        if ( ! file_exists( $upload_path ) ) {
            wp_mkdir_p( $upload_path );

            // Add .htaccess for security
            $htaccess_content = "Options -Indexes\n";
            if ( $type === 'model' ) {
                $htaccess_content .= "AddType model/gltf-binary .glb\n";
                $htaccess_content .= "AddType model/gltf+json .gltf\n";
            }
            file_put_contents( $upload_path . '/.htaccess', $htaccess_content );
        }

        return array(
            'path' => $upload_path,
            'url' => $upload_url,
        );
    }

    /**
     * Generate unique filename
     *
     * @param string $filename Original filename.
     * @param string $upload_path Upload directory path.
     * @return string Unique filename.
     */
    private function generate_unique_filename( $filename, $upload_path ) {
        $file_info = pathinfo( $filename );
        $name = sanitize_file_name( $file_info['filename'] );
        $ext = $file_info['extension'];

        $unique_filename = $name . '.' . $ext;
        $counter = 1;

        while ( file_exists( $upload_path . '/' . $unique_filename ) ) {
            $unique_filename = $name . '-' . $counter . '.' . $ext;
            $counter++;
        }

        return $unique_filename;
    }

    /**
     * Get allowed formats for file type
     *
     * @param string $type File type.
     * @return array Allowed formats.
     */
    private function get_allowed_formats( $type ) {
        switch ( $type ) {
            case 'model':
                return self::ALLOWED_3D_FORMATS;
            case 'video':
                return self::ALLOWED_VIDEO_FORMATS;
            case 'image':
                return self::ALLOWED_IMAGE_FORMATS;
            default:
                return array();
        }
    }

    /**
     * Get maximum file size for type
     *
     * @param string $type File type.
     * @return int Maximum file size in bytes.
     */
    private function get_max_file_size( $type ) {
        $wp_max_size = wp_max_upload_size();

        switch ( $type ) {
            case 'model':
                return min( $wp_max_size, 100 * 1024 * 1024 ); // 100MB for 3D models
            case 'video':
                return min( $wp_max_size, 500 * 1024 * 1024 ); // 500MB for videos
            case 'image':
                return min( $wp_max_size, 10 * 1024 * 1024 ); // 10MB for images
            default:
                return $wp_max_size;
        }
    }

    /**
     * Get upload error message
     *
     * @param int $error_code PHP upload error code.
     * @return string Error message.
     */
    private function get_upload_error_message( $error_code ) {
        switch ( $error_code ) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return __( 'File is too large.', 'voxel-media-integrator' );
            case UPLOAD_ERR_PARTIAL:
                return __( 'File was only partially uploaded.', 'voxel-media-integrator' );
            case UPLOAD_ERR_NO_FILE:
                return __( 'No file was uploaded.', 'voxel-media-integrator' );
            case UPLOAD_ERR_NO_TMP_DIR:
                return __( 'Missing temporary folder.', 'voxel-media-integrator' );
            case UPLOAD_ERR_CANT_WRITE:
                return __( 'Failed to write file to disk.', 'voxel-media-integrator' );
            case UPLOAD_ERR_EXTENSION:
                return __( 'File upload stopped by extension.', 'voxel-media-integrator' );
            default:
                return __( 'Unknown upload error.', 'voxel-media-integrator' );
        }
    }
}
