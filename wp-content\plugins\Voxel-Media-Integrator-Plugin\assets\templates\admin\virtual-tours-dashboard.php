<?php
/**
 * Template for displaying virtual tours
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-tours">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($draft_tours > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft virtual tour that needs attention.',
                    'You have %d draft virtual tours that need attention.',
                    $draft_tours,
                    'voxel-media-integrator'
                ),
                $draft_tours
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-location"></span>
                <?php esc_html_e('Published Tours', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_tours); ?></p>
            <p class="vmi-stat-label"><?php
                echo esc_html(sprintf(
                    __('%d in review', 'voxel-media-integrator'),
                    isset($pending_tours) ? $pending_tours : 0
                ));
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-admin-site-alt3"></span>
                <?php esc_html_e('Total Tours', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_tours + $draft_tours + $pending_tours); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('All virtual experiences', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-visibility"></span>
                <?php esc_html_e('Total Views', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php
                $total_views = rand(1000, 9999); // Mock data - replace with actual
                echo esc_html(number_format($total_views));
            ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Across all tours', 'voxel-media-integrator'); ?></p>
        </div>
    </div>

    <div class="vmi-quick-actions">
        <a href="<?php echo esc_url(admin_url('post-new.php?post_type=' . VMI_Virtual_Tours::CPT_SLUG)); ?>" class="button button-primary">
            <span class="dashicons dashicons-plus-alt2"></span>
            <?php esc_html_e('Add New Tour', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('edit.php?post_type=' . VMI_Virtual_Tours::CPT_SLUG)); ?>" class="button">
            <span class="dashicons dashicons-list-view"></span>
            <?php esc_html_e('View All Tours', 'voxel-media-integrator'); ?>
        </a>

        <a href="<?php echo esc_url(admin_url('edit-tags.php?taxonomy=vmi_media_category&post_type=' . VMI_Virtual_Tours::CPT_SLUG)); ?>" class="button">
            <span class="dashicons dashicons-tag"></span>
            <?php esc_html_e('Tour Categories', 'voxel-media-integrator'); ?>
        </a>

        <a href="#vmi-tours-settings" class="button vmi-settings-toggle">
            <span class="dashicons dashicons-admin-settings"></span>
            <?php esc_html_e('Tour Settings', 'voxel-media-integrator'); ?>
        </a>
    </div>

    <!-- Virtual Tours Settings Panel -->
    <div id="vmi-tours-settings" class="vmi-settings-panel" style="display: none;">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-admin-generic"></span>
                <?php esc_html_e('Virtual Tours Settings', 'voxel-media-integrator'); ?>
            </h2>

            <form method="post" action="options.php" class="vmi-settings-form">
                <?php
                settings_fields('vmi_virtual_tours_settings');
                $tours_settings = get_option('vmi_virtual_tours_settings', array());
                ?>

                <div class="vmi-settings-grid">
                    <!-- General Settings -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('General Settings', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_wpvr">
                                <input type="checkbox" id="vmi_enable_wpvr" name="vmi_virtual_tours_settings[enable_wpvr]" value="1"
                                    <?php checked(isset($tours_settings['enable_wpvr']) ? $tours_settings['enable_wpvr'] : 1, 1); ?>>
                                <?php esc_html_e('Enable WPVR Integration', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Integrate with WPVR plugin for virtual reality tours.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_3d_vista">
                                <input type="checkbox" id="vmi_enable_3d_vista" name="vmi_virtual_tours_settings[enable_3d_vista]" value="1"
                                    <?php checked(isset($tours_settings['enable_3d_vista']) ? $tours_settings['enable_3d_vista'] : 1, 1); ?>>
                                <?php esc_html_e('Enable 3D Vista Integration', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow embedding of 3D Vista virtual tours.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_default_tour_height"><?php esc_html_e('Default Tour Height (px)', 'voxel-media-integrator'); ?></label>
                            <input type="number" id="vmi_default_tour_height" name="vmi_virtual_tours_settings[default_tour_height]"
                                value="<?php echo esc_attr(isset($tours_settings['default_tour_height']) ? $tours_settings['default_tour_height'] : 400); ?>"
                                min="200" max="1000" step="10">
                            <p class="description"><?php esc_html_e('Default height for virtual tours in pixels.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_fullscreen">
                                <input type="checkbox" id="vmi_enable_fullscreen" name="vmi_virtual_tours_settings[enable_fullscreen]" value="1"
                                    <?php checked(isset($tours_settings['enable_fullscreen']) ? $tours_settings['enable_fullscreen'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Fullscreen Mode', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow users to view tours in fullscreen mode.', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>

                    <!-- User Permissions -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('User Permissions', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_tour_creation_role"><?php esc_html_e('Tour Creation Role', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_tour_creation_role" name="vmi_virtual_tours_settings[tour_creation_role]">
                                <option value="administrator" <?php selected(isset($tours_settings['tour_creation_role']) ? $tours_settings['tour_creation_role'] : 'administrator', 'administrator'); ?>>
                                    <?php esc_html_e('Administrator', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="editor" <?php selected(isset($tours_settings['tour_creation_role']) ? $tours_settings['tour_creation_role'] : 'administrator', 'editor'); ?>>
                                    <?php esc_html_e('Editor', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="author" <?php selected(isset($tours_settings['tour_creation_role']) ? $tours_settings['tour_creation_role'] : 'administrator', 'author'); ?>>
                                    <?php esc_html_e('Author', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="contributor" <?php selected(isset($tours_settings['tour_creation_role']) ? $tours_settings['tour_creation_role'] : 'administrator', 'contributor'); ?>>
                                    <?php esc_html_e('Contributor', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="subscriber" <?php selected(isset($tours_settings['tour_creation_role']) ? $tours_settings['tour_creation_role'] : 'administrator', 'subscriber'); ?>>
                                    <?php esc_html_e('Subscriber', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Minimum user role required to create virtual tours.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_tour_moderation"><?php esc_html_e('Tour Moderation', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_tour_moderation" name="vmi_virtual_tours_settings[tour_moderation]">
                                <option value="publish" <?php selected(isset($tours_settings['tour_moderation']) ? $tours_settings['tour_moderation'] : 'pending', 'publish'); ?>>
                                    <?php esc_html_e('Auto-publish tours', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="pending" <?php selected(isset($tours_settings['tour_moderation']) ? $tours_settings['tour_moderation'] : 'pending', 'pending'); ?>>
                                    <?php esc_html_e('Require admin approval', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Choose whether virtual tours need approval before publishing.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_max_tours"><?php esc_html_e('Maximum Tours Per User', 'voxel-media-integrator'); ?></label>
                            <input type="number" id="vmi_max_tours" name="vmi_virtual_tours_settings[max_tours]"
                                value="<?php echo esc_attr(isset($tours_settings['max_tours']) ? $tours_settings['max_tours'] : 0); ?>"
                                min="0" step="1">
                            <p class="description"><?php esc_html_e('Maximum number of virtual tours a user can create (0 = unlimited).', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>

                    <!-- Integration Settings -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('Integration Settings', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_business">
                                <input type="checkbox" id="vmi_enable_business" name="vmi_virtual_tours_settings[enable_business]" value="1"
                                    <?php checked(isset($tours_settings['enable_business']) ? $tours_settings['enable_business'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Business Integration', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow virtual tours to be associated with business listings.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_business_field_id"><?php esc_html_e('Business Field ID', 'voxel-media-integrator'); ?></label>
                            <input type="text" id="vmi_business_field_id" name="vmi_virtual_tours_settings[business_field_id]"
                                value="<?php echo esc_attr(isset($tours_settings['business_field_id']) ? $tours_settings['business_field_id'] : ''); ?>">
                            <p class="description"><?php esc_html_e('Enter the field ID used to associate virtual tours with business listings.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_timeline">
                                <input type="checkbox" id="vmi_enable_timeline" name="vmi_virtual_tours_settings[enable_timeline]" value="1"
                                    <?php checked(isset($tours_settings['enable_timeline']) ? $tours_settings['enable_timeline'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Timeline Integration', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow users to share virtual tours in the Voxel timeline.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_color_theme"><?php esc_html_e('Color Theme', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_color_theme" name="vmi_virtual_tours_settings[color_theme]">
                                <option value="default" <?php selected(isset($tours_settings['color_theme']) ? $tours_settings['color_theme'] : 'default', 'default'); ?>>
                                    <?php esc_html_e('Default', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="dark" <?php selected(isset($tours_settings['color_theme']) ? $tours_settings['color_theme'] : 'default', 'dark'); ?>>
                                    <?php esc_html_e('Dark Theme', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="light" <?php selected(isset($tours_settings['color_theme']) ? $tours_settings['color_theme'] : 'default', 'light'); ?>>
                                    <?php esc_html_e('Light Theme', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="custom" <?php selected(isset($tours_settings['color_theme']) ? $tours_settings['color_theme'] : 'default', 'custom'); ?>>
                                    <?php esc_html_e('Custom Theme', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Choose the color theme for virtual tours.', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="vmi-settings-actions">
                    <?php submit_button(__('Save Virtual Tours Settings', 'voxel-media-integrator'), 'primary', 'submit', false); ?>
                    <button type="button" class="button vmi-settings-toggle"><?php esc_html_e('Cancel', 'voxel-media-integrator'); ?></button>
                </div>
            </form>
        </div>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Tours', 'voxel-media-integrator'); ?>
            </h2>

            <?php if (!empty($recent_tours)): ?>
                <?php foreach ($recent_tours as $tour): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-location"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url(get_edit_post_link($tour->ID)); ?>">
                                    <?php echo esc_html($tour->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php
                                    $tour_type = get_post_meta($tour->ID, 'vmi_tour_type', true);
                                    echo sprintf(
                                        '%s &bull; %s',
                                        esc_html(ucfirst($tour_type ?: 'Standard')),
                                        esc_html(sprintf(
                                            __('Added %s ago', 'voxel-media-integrator'),
                                            human_time_diff(get_post_time('U', false, $tour), current_time('timestamp'))
                                        ))
                                    );
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent tours found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Tour Types Distribution', 'voxel-media-integrator'); ?>
            </h2>

            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-building"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Property Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d tour', '%d tours', isset($property_tours) ? $property_tours : 0, 'voxel-media-integrator'),
                                isset($property_tours) ? $property_tours : 0
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-art"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Gallery Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d tour', '%d tours', isset($gallery_tours) ? $gallery_tours : 0, 'voxel-media-integrator'),
                                isset($gallery_tours) ? $gallery_tours : 0
                            )); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-store"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Business Tours', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php echo esc_html(sprintf(
                                _n('%d tour', '%d tours', isset($business_tours) ? $business_tours : 0, 'voxel-media-integrator'),
                                isset($business_tours) ? $business_tours : 0
                            )); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.vmi-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vmi-setting-group {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
}

.vmi-setting-group h3 {
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #f1f1f1;
    color: #23282d;
    font-size: 16px;
}

.vmi-setting-item {
    margin-bottom: 20px;
}

.vmi-setting-item:last-child {
    margin-bottom: 0;
}

.vmi-setting-item label {
    font-weight: 600;
    color: #23282d;
    margin-bottom: 5px;
    display: block;
}

.vmi-setting-item input[type="number"],
.vmi-setting-item input[type="text"],
.vmi-setting-item select {
    width: 100%;
    max-width: 300px;
}

.vmi-settings-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 10px;
    align-items: center;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Virtual Tours settings toggle functionality
    $('.vmi-settings-toggle').on('click', function(e) {
        e.preventDefault();
        const settingsPanel = $('#vmi-tours-settings');

        if (settingsPanel.is(':visible')) {
            settingsPanel.slideUp(300);
        } else {
            settingsPanel.slideDown(300);
        }
    });

    // Auto-hide settings panel when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#vmi-tours-settings, .vmi-settings-toggle').length) {
            $('#vmi-tours-settings').slideUp(300);
        }
    });

    // Form validation and enhanced UX
    $('#vmi-tours-settings form').on('submit', function(e) {
        // Add loading state
        const submitBtn = $(this).find('input[type="submit"]');
        const originalText = submitBtn.val();

        submitBtn.val('<?php esc_html_e('Saving...', 'voxel-media-integrator'); ?>').prop('disabled', true);

        // Re-enable after a delay (WordPress will handle the actual save)
        setTimeout(function() {
            submitBtn.val(originalText).prop('disabled', false);
        }, 2000);
    });
});
</script>
