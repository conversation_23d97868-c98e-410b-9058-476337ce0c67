<?php
/**
 * Shortcodes for displaying media
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class for handling shortcodes
 */
class VMI_Shortcodes {

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', array( $this, 'register_shortcodes' ) );
    }

    /**
     * Register all shortcodes
     */
    public function register_shortcodes() {
        add_shortcode( 'vmi_3d_model', array( $this, 'render_3d_model' ) );
        add_shortcode( 'vmi_video', array( $this, 'render_video' ) );
        add_shortcode( 'vmi_virtual_tour', array( $this, 'render_virtual_tour' ) );
        add_shortcode( 'vmi_course', array( $this, 'render_course' ) );
        add_shortcode( 'vmi_media_gallery', array( $this, 'render_media_gallery' ) );
    }

    /**
     * Render 3D model shortcode
     * 
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_3d_model( $atts ) {
        $atts = shortcode_atts( array(
            'id' => '',
            'width' => '100%',
            'height' => '400px',
            'auto_rotate' => 'true',
            'camera_controls' => 'true',
            'environment_image' => '',
            'poster' => '',
        ), $atts, 'vmi_3d_model' );

        if ( empty( $atts['id'] ) ) {
            return '<p>' . __( 'Please provide a 3D model ID.', 'voxel-media-integrator' ) . '</p>';
        }

        $post = get_post( $atts['id'] );
        if ( ! $post || $post->post_type !== 'vmi_3d_models' ) {
            return '<p>' . __( 'Invalid 3D model ID.', 'voxel-media-integrator' ) . '</p>';
        }

        $model_file = get_post_meta( $post->ID, 'vmi_model_file', true );
        if ( empty( $model_file ) ) {
            return '<p>' . __( 'No 3D model file found.', 'voxel-media-integrator' ) . '</p>';
        }

        // Enqueue model viewer script
        wp_enqueue_script( 'model-viewer' );

        $poster_attr = '';
        if ( ! empty( $atts['poster'] ) ) {
            $poster_attr = 'poster="' . esc_url( $atts['poster'] ) . '"';
        } elseif ( has_post_thumbnail( $post->ID ) ) {
            $poster_attr = 'poster="' . esc_url( get_the_post_thumbnail_url( $post->ID, 'large' ) ) . '"';
        }

        $auto_rotate_attr = $atts['auto_rotate'] === 'true' ? 'auto-rotate' : '';
        $camera_controls_attr = $atts['camera_controls'] === 'true' ? 'camera-controls' : '';
        $environment_attr = ! empty( $atts['environment_image'] ) ? 'environment-image="' . esc_url( $atts['environment_image'] ) . '"' : '';

        $output = '<div class="vmi-3d-model-container" style="width: ' . esc_attr( $atts['width'] ) . '; height: ' . esc_attr( $atts['height'] ) . ';">';
        $output .= '<model-viewer src="' . esc_url( $model_file ) . '" ';
        $output .= $poster_attr . ' ';
        $output .= $auto_rotate_attr . ' ';
        $output .= $camera_controls_attr . ' ';
        $output .= $environment_attr . ' ';
        $output .= 'style="width: 100%; height: 100%;" ';
        $output .= 'alt="' . esc_attr( $post->post_title ) . '">';
        $output .= '</model-viewer>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render video shortcode
     * 
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_video( $atts ) {
        $atts = shortcode_atts( array(
            'id' => '',
            'width' => '100%',
            'height' => '400px',
            'autoplay' => 'false',
            'controls' => 'true',
            'muted' => 'false',
            'loop' => 'false',
        ), $atts, 'vmi_video' );

        if ( empty( $atts['id'] ) ) {
            return '<p>' . __( 'Please provide a video ID.', 'voxel-media-integrator' ) . '</p>';
        }

        $post = get_post( $atts['id'] );
        if ( ! $post || $post->post_type !== 'vmi_videos' ) {
            return '<p>' . __( 'Invalid video ID.', 'voxel-media-integrator' ) . '</p>';
        }

        $video_url = get_post_meta( $post->ID, 'vmi_video_url', true );
        $video_type = get_post_meta( $post->ID, 'vmi_video_type', true );

        if ( empty( $video_url ) ) {
            return '<p>' . __( 'No video URL found.', 'voxel-media-integrator' ) . '</p>';
        }

        // Handle different video types
        if ( $video_type === 'youtube' ) {
            return $this->render_youtube_video( $video_url, $atts );
        } elseif ( $video_type === 'vimeo' ) {
            return $this->render_vimeo_video( $video_url, $atts );
        } else {
            return $this->render_self_hosted_video( $video_url, $atts, $post );
        }
    }

    /**
     * Render YouTube video
     */
    private function render_youtube_video( $url, $atts ) {
        // Extract video ID from YouTube URL
        preg_match( '/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $url, $matches );
        $video_id = isset( $matches[1] ) ? $matches[1] : '';

        if ( empty( $video_id ) ) {
            return '<p>' . __( 'Invalid YouTube URL.', 'voxel-media-integrator' ) . '</p>';
        }

        $autoplay = $atts['autoplay'] === 'true' ? '&autoplay=1' : '';
        $muted = $atts['muted'] === 'true' ? '&mute=1' : '';
        $loop = $atts['loop'] === 'true' ? '&loop=1&playlist=' . $video_id : '';

        $output = '<div class="vmi-video-container" style="width: ' . esc_attr( $atts['width'] ) . '; height: ' . esc_attr( $atts['height'] ) . ';">';
        $output .= '<iframe src="https://www.youtube.com/embed/' . esc_attr( $video_id ) . '?rel=0' . $autoplay . $muted . $loop . '" ';
        $output .= 'width="100%" height="100%" frameborder="0" allowfullscreen></iframe>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render Vimeo video
     */
    private function render_vimeo_video( $url, $atts ) {
        // Extract video ID from Vimeo URL
        preg_match( '/vimeo\.com\/(\d+)/', $url, $matches );
        $video_id = isset( $matches[1] ) ? $matches[1] : '';

        if ( empty( $video_id ) ) {
            return '<p>' . __( 'Invalid Vimeo URL.', 'voxel-media-integrator' ) . '</p>';
        }

        $autoplay = $atts['autoplay'] === 'true' ? '&autoplay=1' : '';
        $muted = $atts['muted'] === 'true' ? '&muted=1' : '';
        $loop = $atts['loop'] === 'true' ? '&loop=1' : '';

        $output = '<div class="vmi-video-container" style="width: ' . esc_attr( $atts['width'] ) . '; height: ' . esc_attr( $atts['height'] ) . ';">';
        $output .= '<iframe src="https://player.vimeo.com/video/' . esc_attr( $video_id ) . '?title=0&byline=0&portrait=0' . $autoplay . $muted . $loop . '" ';
        $output .= 'width="100%" height="100%" frameborder="0" allowfullscreen></iframe>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render self-hosted video
     */
    private function render_self_hosted_video( $url, $atts, $post ) {
        $autoplay = $atts['autoplay'] === 'true' ? 'autoplay' : '';
        $controls = $atts['controls'] === 'true' ? 'controls' : '';
        $muted = $atts['muted'] === 'true' ? 'muted' : '';
        $loop = $atts['loop'] === 'true' ? 'loop' : '';

        $poster_attr = '';
        if ( has_post_thumbnail( $post->ID ) ) {
            $poster_attr = 'poster="' . esc_url( get_the_post_thumbnail_url( $post->ID, 'large' ) ) . '"';
        }

        $output = '<div class="vmi-video-container" style="width: ' . esc_attr( $atts['width'] ) . '; height: ' . esc_attr( $atts['height'] ) . ';">';
        $output .= '<video ' . $controls . ' ' . $autoplay . ' ' . $muted . ' ' . $loop . ' ' . $poster_attr . ' style="width: 100%; height: 100%;">';
        $output .= '<source src="' . esc_url( $url ) . '" type="video/mp4">';
        $output .= '<p>' . __( 'Your browser does not support the video tag.', 'voxel-media-integrator' ) . '</p>';
        $output .= '</video>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Render virtual tour shortcode
     * 
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_virtual_tour( $atts ) {
        $atts = shortcode_atts( array(
            'id' => '',
            'width' => '100%',
            'height' => '400px',
            'auto_load' => 'true',
        ), $atts, 'vmi_virtual_tour' );

        if ( empty( $atts['id'] ) ) {
            return '<p>' . __( 'Please provide a virtual tour ID.', 'voxel-media-integrator' ) . '</p>';
        }

        $post = get_post( $atts['id'] );
        if ( ! $post || $post->post_type !== 'vmi_virtual_tours' ) {
            return '<p>' . __( 'Invalid virtual tour ID.', 'voxel-media-integrator' ) . '</p>';
        }

        // Enqueue Pannellum scripts
        wp_enqueue_script( 'pannellum' );
        wp_enqueue_style( 'pannellum' );

        $tour_config = get_post_meta( $post->ID, 'vmi_tour_config', true );
        if ( empty( $tour_config ) ) {
            return '<p>' . __( 'No tour configuration found.', 'voxel-media-integrator' ) . '</p>';
        }

        $unique_id = 'vmi-tour-' . $post->ID . '-' . wp_rand();

        $output = '<div class="vmi-virtual-tour-container" style="width: ' . esc_attr( $atts['width'] ) . '; height: ' . esc_attr( $atts['height'] ) . ';">';
        $output .= '<div id="' . esc_attr( $unique_id ) . '" style="width: 100%; height: 100%;"></div>';
        $output .= '</div>';

        // Add inline script to initialize the tour
        $auto_load = $atts['auto_load'] === 'true' ? 'true' : 'false';
        $output .= '<script>';
        $output .= 'document.addEventListener("DOMContentLoaded", function() {';
        $output .= 'pannellum.viewer("' . esc_js( $unique_id ) . '", ' . wp_json_encode( json_decode( $tour_config, true ) ) . ');';
        $output .= '});';
        $output .= '</script>';

        return $output;
    }

    /**
     * Render course shortcode
     * 
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_course( $atts ) {
        $atts = shortcode_atts( array(
            'id' => '',
            'show_lessons' => 'true',
            'show_progress' => 'true',
        ), $atts, 'vmi_course' );

        if ( empty( $atts['id'] ) ) {
            return '<p>' . __( 'Please provide a course ID.', 'voxel-media-integrator' ) . '</p>';
        }

        $post = get_post( $atts['id'] );
        if ( ! $post || $post->post_type !== 'vmi_courses' ) {
            return '<p>' . __( 'Invalid course ID.', 'voxel-media-integrator' ) . '</p>';
        }

        $output = '<div class="vmi-course-container">';
        $output .= '<h3>' . esc_html( $post->post_title ) . '</h3>';
        $output .= '<div class="vmi-course-content">' . apply_filters( 'the_content', $post->post_content ) . '</div>';

        if ( $atts['show_lessons'] === 'true' ) {
            $lessons = get_posts( array(
                'post_type' => 'vmi_lessons',
                'meta_query' => array(
                    array(
                        'key' => 'vmi_lesson_course',
                        'value' => $post->ID,
                        'compare' => '='
                    )
                ),
                'orderby' => 'menu_order',
                'order' => 'ASC',
                'posts_per_page' => -1,
            ) );

            if ( ! empty( $lessons ) ) {
                $output .= '<div class="vmi-course-lessons">';
                $output .= '<h4>' . __( 'Lessons', 'voxel-media-integrator' ) . '</h4>';
                $output .= '<ul>';
                foreach ( $lessons as $lesson ) {
                    $output .= '<li><a href="' . esc_url( get_permalink( $lesson->ID ) ) . '">' . esc_html( $lesson->post_title ) . '</a></li>';
                }
                $output .= '</ul>';
                $output .= '</div>';
            }
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * Render media gallery shortcode
     * 
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render_media_gallery( $atts ) {
        $atts = shortcode_atts( array(
            'type' => 'all', // all, 3d_models, videos, virtual_tours
            'limit' => '12',
            'columns' => '3',
            'category' => '',
            'orderby' => 'date',
            'order' => 'DESC',
        ), $atts, 'vmi_media_gallery' );

        $post_types = array();
        switch ( $atts['type'] ) {
            case '3d_models':
                $post_types = array( 'vmi_3d_models' );
                break;
            case 'videos':
                $post_types = array( 'vmi_videos' );
                break;
            case 'virtual_tours':
                $post_types = array( 'vmi_virtual_tours' );
                break;
            default:
                $post_types = array( 'vmi_3d_models', 'vmi_videos', 'vmi_virtual_tours' );
                break;
        }

        $query_args = array(
            'post_type' => $post_types,
            'posts_per_page' => intval( $atts['limit'] ),
            'orderby' => $atts['orderby'],
            'order' => $atts['order'],
            'post_status' => 'publish',
        );

        if ( ! empty( $atts['category'] ) ) {
            $query_args['tax_query'] = array(
                array(
                    'taxonomy' => 'vmi_media_category',
                    'field' => 'slug',
                    'terms' => $atts['category'],
                )
            );
        }

        $media_query = new WP_Query( $query_args );

        if ( ! $media_query->have_posts() ) {
            return '<p>' . __( 'No media found.', 'voxel-media-integrator' ) . '</p>';
        }

        $columns = max( 1, min( 6, intval( $atts['columns'] ) ) );
        $column_width = floor( 100 / $columns );

        $output = '<div class="vmi-media-gallery vmi-columns-' . esc_attr( $columns ) . '">';

        while ( $media_query->have_posts() ) {
            $media_query->the_post();
            $post_type = get_post_type();
            
            $output .= '<div class="vmi-media-item" style="width: ' . $column_width . '%;">';
            $output .= '<div class="vmi-media-item-inner">';
            
            if ( has_post_thumbnail() ) {
                $output .= '<div class="vmi-media-thumbnail">';
                $output .= '<a href="' . esc_url( get_permalink() ) . '">' . get_the_post_thumbnail( get_the_ID(), 'medium' ) . '</a>';
                $output .= '</div>';
            }
            
            $output .= '<div class="vmi-media-content">';
            $output .= '<h4><a href="' . esc_url( get_permalink() ) . '">' . esc_html( get_the_title() ) . '</a></h4>';
            $output .= '<p class="vmi-media-type">' . esc_html( ucwords( str_replace( array( 'vmi_', '_' ), array( '', ' ' ), $post_type ) ) ) . '</p>';
            if ( has_excerpt() ) {
                $output .= '<p class="vmi-media-excerpt">' . esc_html( get_the_excerpt() ) . '</p>';
            }
            $output .= '</div>';
            
            $output .= '</div>';
            $output .= '</div>';
        }

        $output .= '</div>';

        wp_reset_postdata();

        return $output;
    }
}
