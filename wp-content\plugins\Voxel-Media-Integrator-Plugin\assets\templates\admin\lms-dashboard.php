<?php
/**
 * Template for displaying LMS
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap vmi-theme-lms">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <?php if ($draft_courses > 0): ?>
        <div class="vmi-warning-message">
            <?php echo esc_html(sprintf(
                _n(
                    'You have %d draft course that needs attention.',
                    'You have %d draft courses that need attention.',
                    $draft_courses,
                    'voxel-media-integrator'
                ),
                $draft_courses
            )); ?>
        </div>
    <?php endif; ?>

    <div class="vmi-stats-grid">
        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-welcome-learn-more"></span>
                <?php esc_html_e('Total Courses', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_courses + $draft_courses + $pending_courses); ?></p>
            <p class="vmi-stat-label"><?php
                echo esc_html(sprintf(
                    __('%d published', 'voxel-media-integrator'),
                    isset($published_courses) ? $published_courses : 0
                ));
            ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-book"></span>
                <?php esc_html_e('Total Lessons', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php echo esc_html($published_lessons); ?></p>
            <p class="vmi-stat-label"><?php esc_html_e('Across all courses', 'voxel-media-integrator'); ?></p>
        </div>

        <div class="vmi-stat-card">
            <h3>
                <span class="dashicons dashicons-groups"></span>
                <?php esc_html_e('Students Enrolled', 'voxel-media-integrator'); ?>
            </h3>
            <p class="vmi-stat-number"><?php
                $total_students = rand(50, 500); // Mock data - replace with actual
                echo esc_html($total_students);
            ?></p>
            <p class="vmi-stat-label"><?php
                $completion_rate = rand(60, 90);
                echo esc_html(sprintf(
                    __('%d%% completion rate', 'voxel-media-integrator'),
                    $completion_rate
                ));
            ?></p>
        </div>
    </div>

    <!-- LMS Navigation Tabs -->
    <div class="vmi-lms-navigation">
        <h2><?php esc_html_e('LMS Management', 'voxel-media-integrator'); ?></h2>

        <div class="vmi-nav-tabs">
            <!-- Course Management Section -->
            <div class="vmi-nav-section">
                <h3><?php esc_html_e('Course Management', 'voxel-media-integrator'); ?></h3>
                <div class="vmi-nav-links">
                    <a href="<?php echo esc_url(admin_url('edit.php?post_type=vmi_courses')); ?>" class="vmi-nav-link">
                        <span class="dashicons dashicons-welcome-learn-more"></span>
                        <?php esc_html_e('All Courses', 'voxel-media-integrator'); ?>
                    </a>
                    <a href="<?php echo esc_url(admin_url('post-new.php?post_type=vmi_courses')); ?>" class="vmi-nav-link button-primary">
                        <span class="dashicons dashicons-plus-alt2"></span>
                        <?php esc_html_e('Add New Course', 'voxel-media-integrator'); ?>
                    </a>
                    <a href="<?php echo esc_url(admin_url('edit-tags.php?taxonomy=vmi_course_category&post_type=vmi_courses')); ?>" class="vmi-nav-link">
                        <span class="dashicons dashicons-category"></span>
                        <?php esc_html_e('Course Categories', 'voxel-media-integrator'); ?>
                    </a>
                </div>
            </div>

            <!-- Lesson Management Section -->
            <div class="vmi-nav-section">
                <h3><?php esc_html_e('Lesson Management', 'voxel-media-integrator'); ?></h3>
                <div class="vmi-nav-links">
                    <a href="<?php echo esc_url(admin_url('edit.php?post_type=vmi_lessons')); ?>" class="vmi-nav-link">
                        <span class="dashicons dashicons-book"></span>
                        <?php esc_html_e('All Lessons', 'voxel-media-integrator'); ?>
                    </a>
                    <a href="<?php echo esc_url(admin_url('post-new.php?post_type=vmi_lessons')); ?>" class="vmi-nav-link">
                        <span class="dashicons dashicons-plus-alt2"></span>
                        <?php esc_html_e('Add New Lesson', 'voxel-media-integrator'); ?>
                    </a>
                </div>
            </div>

            <!-- Quiz Management Section -->
            <div class="vmi-nav-section">
                <h3><?php esc_html_e('Quiz Management', 'voxel-media-integrator'); ?></h3>
                <div class="vmi-nav-links">
                    <a href="<?php echo esc_url(admin_url('edit.php?post_type=vmi_quizzes')); ?>" class="vmi-nav-link">
                        <span class="dashicons dashicons-editor-help"></span>
                        <?php esc_html_e('All Quizzes', 'voxel-media-integrator'); ?>
                    </a>
                    <a href="<?php echo esc_url(admin_url('post-new.php?post_type=vmi_quizzes')); ?>" class="vmi-nav-link">
                        <span class="dashicons dashicons-plus-alt2"></span>
                        <?php esc_html_e('Add New Quiz', 'voxel-media-integrator'); ?>
                    </a>
                </div>
            </div>

            <!-- Student & Analytics Section -->
            <div class="vmi-nav-section">
                <h3><?php esc_html_e('Students & Analytics', 'voxel-media-integrator'); ?></h3>
                <div class="vmi-nav-links">
                    <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-students')); ?>" class="vmi-nav-link">
                        <span class="dashicons dashicons-groups"></span>
                        <?php esc_html_e('Students', 'voxel-media-integrator'); ?>
                    </a>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=vmi-reports')); ?>" class="vmi-nav-link">
                        <span class="dashicons dashicons-chart-line"></span>
                        <?php esc_html_e('Reports', 'voxel-media-integrator'); ?>
                    </a>
                    <a href="#vmi-lms-settings" class="vmi-nav-link vmi-settings-toggle">
                        <span class="dashicons dashicons-admin-generic"></span>
                        <?php esc_html_e('LMS Settings', 'voxel-media-integrator'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- LMS Settings Panel -->
    <div id="vmi-lms-settings" class="vmi-settings-panel" style="display: none;">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-admin-generic"></span>
                <?php esc_html_e('LMS Settings', 'voxel-media-integrator'); ?>
            </h2>

            <form method="post" action="options.php" class="vmi-settings-form">
                <?php
                settings_fields('vmi_lms_settings');
                $lms_settings = get_option('vmi_lms_settings', array());
                ?>

                <div class="vmi-settings-grid">
                    <!-- General Settings -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('General Settings', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_tutor_lms">
                                <input type="checkbox" id="vmi_enable_tutor_lms" name="vmi_lms_settings[enable_tutor_lms]" value="1"
                                    <?php checked(isset($lms_settings['enable_tutor_lms']) ? $lms_settings['enable_tutor_lms'] : 0, 1); ?>>
                                <?php esc_html_e('Enable Tutor LMS Integration', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Integrate with Tutor LMS plugin for enhanced functionality.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_categories">
                                <input type="checkbox" id="vmi_enable_categories" name="vmi_lms_settings[enable_categories]" value="1"
                                    <?php checked(isset($lms_settings['enable_categories']) ? $lms_settings['enable_categories'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Course Categories', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow courses to be organized into categories.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_pricing">
                                <input type="checkbox" id="vmi_enable_pricing" name="vmi_lms_settings[enable_pricing]" value="1"
                                    <?php checked(isset($lms_settings['enable_pricing']) ? $lms_settings['enable_pricing'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Course Pricing', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow courses to have pricing and payment integration.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_course_layout"><?php esc_html_e('Course Layout', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_course_layout" name="vmi_lms_settings[course_layout]">
                                <option value="grid" <?php selected(isset($lms_settings['course_layout']) ? $lms_settings['course_layout'] : 'grid', 'grid'); ?>>
                                    <?php esc_html_e('Grid Layout', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="list" <?php selected(isset($lms_settings['course_layout']) ? $lms_settings['course_layout'] : 'grid', 'list'); ?>>
                                    <?php esc_html_e('List Layout', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Default layout for displaying courses in archives.', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>

                    <!-- User Permissions -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('User Permissions', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_course_creation_role"><?php esc_html_e('Course Creation Role', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_course_creation_role" name="vmi_lms_settings[course_creation_role]">
                                <option value="administrator" <?php selected(isset($lms_settings['course_creation_role']) ? $lms_settings['course_creation_role'] : 'administrator', 'administrator'); ?>>
                                    <?php esc_html_e('Administrator', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="editor" <?php selected(isset($lms_settings['course_creation_role']) ? $lms_settings['course_creation_role'] : 'administrator', 'editor'); ?>>
                                    <?php esc_html_e('Editor', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="author" <?php selected(isset($lms_settings['course_creation_role']) ? $lms_settings['course_creation_role'] : 'administrator', 'author'); ?>>
                                    <?php esc_html_e('Author', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="contributor" <?php selected(isset($lms_settings['course_creation_role']) ? $lms_settings['course_creation_role'] : 'administrator', 'contributor'); ?>>
                                    <?php esc_html_e('Contributor', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="subscriber" <?php selected(isset($lms_settings['course_creation_role']) ? $lms_settings['course_creation_role'] : 'administrator', 'subscriber'); ?>>
                                    <?php esc_html_e('Subscriber', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Minimum user role required to create courses.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_course_moderation"><?php esc_html_e('Course Moderation', 'voxel-media-integrator'); ?></label>
                            <select id="vmi_course_moderation" name="vmi_lms_settings[course_moderation]">
                                <option value="publish" <?php selected(isset($lms_settings['course_moderation']) ? $lms_settings['course_moderation'] : 'pending', 'publish'); ?>>
                                    <?php esc_html_e('Auto-publish courses', 'voxel-media-integrator'); ?>
                                </option>
                                <option value="pending" <?php selected(isset($lms_settings['course_moderation']) ? $lms_settings['course_moderation'] : 'pending', 'pending'); ?>>
                                    <?php esc_html_e('Require admin approval', 'voxel-media-integrator'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Choose whether courses need approval before publishing.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_max_courses"><?php esc_html_e('Maximum Courses Per User', 'voxel-media-integrator'); ?></label>
                            <input type="number" id="vmi_max_courses" name="vmi_lms_settings[max_courses]"
                                value="<?php echo esc_attr(isset($lms_settings['max_courses']) ? $lms_settings['max_courses'] : 0); ?>"
                                min="0" step="1">
                            <p class="description"><?php esc_html_e('Maximum number of courses a user can create (0 = unlimited).', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>

                    <!-- Integration Settings -->
                    <div class="vmi-setting-group">
                        <h3><?php esc_html_e('Integration Settings', 'voxel-media-integrator'); ?></h3>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_business">
                                <input type="checkbox" id="vmi_enable_business" name="vmi_lms_settings[enable_business]" value="1"
                                    <?php checked(isset($lms_settings['enable_business']) ? $lms_settings['enable_business'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Business Integration', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow courses to be associated with business listings.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_business_field_id"><?php esc_html_e('Business Field ID', 'voxel-media-integrator'); ?></label>
                            <input type="text" id="vmi_business_field_id" name="vmi_lms_settings[business_field_id]"
                                value="<?php echo esc_attr(isset($lms_settings['business_field_id']) ? $lms_settings['business_field_id'] : ''); ?>">
                            <p class="description"><?php esc_html_e('Enter the field ID used to associate courses with business listings.', 'voxel-media-integrator'); ?></p>
                        </div>

                        <div class="vmi-setting-item">
                            <label for="vmi_enable_timeline">
                                <input type="checkbox" id="vmi_enable_timeline" name="vmi_lms_settings[enable_timeline]" value="1"
                                    <?php checked(isset($lms_settings['enable_timeline']) ? $lms_settings['enable_timeline'] : 1, 1); ?>>
                                <?php esc_html_e('Enable Timeline Integration', 'voxel-media-integrator'); ?>
                            </label>
                            <p class="description"><?php esc_html_e('Allow users to share courses in the Voxel timeline.', 'voxel-media-integrator'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="vmi-settings-actions">
                    <?php submit_button(__('Save LMS Settings', 'voxel-media-integrator'), 'primary', 'submit', false); ?>
                    <button type="button" class="button vmi-settings-toggle"><?php esc_html_e('Cancel', 'voxel-media-integrator'); ?></button>
                </div>
            </form>
        </div>
    </div>

    <div class="vmi-content-grid">
        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-clock"></span>
                <?php esc_html_e('Recent Courses', 'voxel-media-integrator'); ?>
            </h2>

            <?php if (!empty($recent_courses)): ?>
                <?php foreach ($recent_courses as $course): ?>
                    <div class="vmi-activity-item">
                        <div class="vmi-activity-icon">
                            <span class="dashicons dashicons-welcome-learn-more"></span>
                        </div>
                        <div class="vmi-activity-content">
                            <div class="vmi-activity-title">
                                <a href="<?php echo esc_url(get_edit_post_link($course->ID)); ?>">
                                    <?php echo esc_html($course->post_title); ?>
                                </a>
                            </div>
                            <div class="vmi-activity-meta">
                                <?php
                                    $lessons_in_course = get_posts(array(
                                        'post_type' => 'vmi_lessons',
                                        'meta_query' => array(
                                            array(
                                                'key' => '_vmi_course_id',
                                                'value' => $course->ID,
                                                'compare' => '='
                                            )
                                        ),
                                        'posts_per_page' => -1,
                                        'fields' => 'ids'
                                    ));

                                    echo sprintf(
                                        '%s &bull; %s',
                                        esc_html(sprintf(
                                            _n('%d lesson', '%d lessons', count($lessons_in_course), 'voxel-media-integrator'),
                                            count($lessons_in_course)
                                        )),
                                        esc_html(sprintf(
                                            __('Added %s ago', 'voxel-media-integrator'),
                                            human_time_diff(get_post_time('U', false, $course), current_time('timestamp'))
                                        ))
                                    );
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="vmi-info-message">
                    <?php esc_html_e('No recent courses found.', 'voxel-media-integrator'); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="vmi-content-card">
            <h2>
                <span class="dashicons dashicons-chart-pie"></span>
                <?php esc_html_e('Learning Activity', 'voxel-media-integrator'); ?>
            </h2>

            <div class="vmi-type-distribution">
                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-welcome-learn-more"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('New Course Created', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php esc_html_e('2 hours ago', 'voxel-media-integrator'); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-groups"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('New Student Enrolled', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php esc_html_e('1 day ago', 'voxel-media-integrator'); ?>
                        </div>
                    </div>
                </div>

                <div class="vmi-activity-item">
                    <div class="vmi-activity-icon">
                        <span class="dashicons dashicons-awards"></span>
                    </div>
                    <div class="vmi-activity-content">
                        <div class="vmi-activity-title">
                            <?php esc_html_e('Course Completed', 'voxel-media-integrator'); ?>
                        </div>
                        <div class="vmi-activity-meta">
                            <?php esc_html_e('3 days ago', 'voxel-media-integrator'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.vmi-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vmi-setting-group {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
}

.vmi-setting-group h3 {
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #f1f1f1;
    color: #23282d;
    font-size: 16px;
}

.vmi-setting-item {
    margin-bottom: 20px;
}

.vmi-setting-item:last-child {
    margin-bottom: 0;
}

.vmi-setting-item label {
    font-weight: 600;
    color: #23282d;
    margin-bottom: 5px;
    display: block;
}

.vmi-setting-item input[type="number"],
.vmi-setting-item input[type="text"],
.vmi-setting-item select {
    width: 100%;
    max-width: 300px;
}

.vmi-settings-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 10px;
    align-items: center;
}
</style>

<script>
jQuery(document).ready(function($) {
    // LMS settings toggle functionality
    $('.vmi-settings-toggle').on('click', function(e) {
        e.preventDefault();
        const settingsPanel = $('#vmi-lms-settings');

        if (settingsPanel.is(':visible')) {
            settingsPanel.slideUp(300);
        } else {
            settingsPanel.slideDown(300);
        }
    });

    // Auto-hide settings panel when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#vmi-lms-settings, .vmi-settings-toggle').length) {
            $('#vmi-lms-settings').slideUp(300);
        }
    });

    // Form validation and enhanced UX
    $('#vmi-lms-settings form').on('submit', function(e) {
        // Add loading state
        const submitBtn = $(this).find('input[type="submit"]');
        const originalText = submitBtn.val();

        submitBtn.val('<?php esc_html_e('Saving...', 'voxel-media-integrator'); ?>').prop('disabled', true);

        // Re-enable after a delay (WordPress will handle the actual save)
        setTimeout(function() {
            submitBtn.val(originalText).prop('disabled', false);
        }, 2000);
    });
});
</script>